
<!-- Generated by webdoc on 28/05/2025, 10.03.48 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: options.js</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"sources"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs sources">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            

<main class="page-content">
  <div class="source">
    <div class="source__split">
      <ul>
        
          <li id="1">
            <a href="#1">1</a>
          </li>
        
          <li id="2">
            <a href="#2">2</a>
          </li>
        
          <li id="3">
            <a href="#3">3</a>
          </li>
        
          <li id="4">
            <a href="#4">4</a>
          </li>
        
          <li id="5">
            <a href="#5">5</a>
          </li>
        
          <li id="6">
            <a href="#6">6</a>
          </li>
        
          <li id="7">
            <a href="#7">7</a>
          </li>
        
          <li id="8">
            <a href="#8">8</a>
          </li>
        
          <li id="9">
            <a href="#9">9</a>
          </li>
        
          <li id="10">
            <a href="#10">10</a>
          </li>
        
          <li id="11">
            <a href="#11">11</a>
          </li>
        
          <li id="12">
            <a href="#12">12</a>
          </li>
        
          <li id="13">
            <a href="#13">13</a>
          </li>
        
          <li id="14">
            <a href="#14">14</a>
          </li>
        
          <li id="15">
            <a href="#15">15</a>
          </li>
        
          <li id="16">
            <a href="#16">16</a>
          </li>
        
          <li id="17">
            <a href="#17">17</a>
          </li>
        
          <li id="18">
            <a href="#18">18</a>
          </li>
        
          <li id="19">
            <a href="#19">19</a>
          </li>
        
          <li id="20">
            <a href="#20">20</a>
          </li>
        
          <li id="21">
            <a href="#21">21</a>
          </li>
        
          <li id="22">
            <a href="#22">22</a>
          </li>
        
          <li id="23">
            <a href="#23">23</a>
          </li>
        
          <li id="24">
            <a href="#24">24</a>
          </li>
        
          <li id="25">
            <a href="#25">25</a>
          </li>
        
          <li id="26">
            <a href="#26">26</a>
          </li>
        
          <li id="27">
            <a href="#27">27</a>
          </li>
        
          <li id="28">
            <a href="#28">28</a>
          </li>
        
          <li id="29">
            <a href="#29">29</a>
          </li>
        
          <li id="30">
            <a href="#30">30</a>
          </li>
        
          <li id="31">
            <a href="#31">31</a>
          </li>
        
          <li id="32">
            <a href="#32">32</a>
          </li>
        
          <li id="33">
            <a href="#33">33</a>
          </li>
        
          <li id="34">
            <a href="#34">34</a>
          </li>
        
          <li id="35">
            <a href="#35">35</a>
          </li>
        
          <li id="36">
            <a href="#36">36</a>
          </li>
        
          <li id="37">
            <a href="#37">37</a>
          </li>
        
          <li id="38">
            <a href="#38">38</a>
          </li>
        
          <li id="39">
            <a href="#39">39</a>
          </li>
        
          <li id="40">
            <a href="#40">40</a>
          </li>
        
          <li id="41">
            <a href="#41">41</a>
          </li>
        
          <li id="42">
            <a href="#42">42</a>
          </li>
        
          <li id="43">
            <a href="#43">43</a>
          </li>
        
          <li id="44">
            <a href="#44">44</a>
          </li>
        
          <li id="45">
            <a href="#45">45</a>
          </li>
        
          <li id="46">
            <a href="#46">46</a>
          </li>
        
          <li id="47">
            <a href="#47">47</a>
          </li>
        
          <li id="48">
            <a href="#48">48</a>
          </li>
        
          <li id="49">
            <a href="#49">49</a>
          </li>
        
          <li id="50">
            <a href="#50">50</a>
          </li>
        
          <li id="51">
            <a href="#51">51</a>
          </li>
        
          <li id="52">
            <a href="#52">52</a>
          </li>
        
          <li id="53">
            <a href="#53">53</a>
          </li>
        
          <li id="54">
            <a href="#54">54</a>
          </li>
        
          <li id="55">
            <a href="#55">55</a>
          </li>
        
          <li id="56">
            <a href="#56">56</a>
          </li>
        
          <li id="57">
            <a href="#57">57</a>
          </li>
        
          <li id="58">
            <a href="#58">58</a>
          </li>
        
          <li id="59">
            <a href="#59">59</a>
          </li>
        
          <li id="60">
            <a href="#60">60</a>
          </li>
        
          <li id="61">
            <a href="#61">61</a>
          </li>
        
          <li id="62">
            <a href="#62">62</a>
          </li>
        
          <li id="63">
            <a href="#63">63</a>
          </li>
        
          <li id="64">
            <a href="#64">64</a>
          </li>
        
          <li id="65">
            <a href="#65">65</a>
          </li>
        
          <li id="66">
            <a href="#66">66</a>
          </li>
        
          <li id="67">
            <a href="#67">67</a>
          </li>
        
          <li id="68">
            <a href="#68">68</a>
          </li>
        
          <li id="69">
            <a href="#69">69</a>
          </li>
        
          <li id="70">
            <a href="#70">70</a>
          </li>
        
          <li id="71">
            <a href="#71">71</a>
          </li>
        
          <li id="72">
            <a href="#72">72</a>
          </li>
        
          <li id="73">
            <a href="#73">73</a>
          </li>
        
          <li id="74">
            <a href="#74">74</a>
          </li>
        
          <li id="75">
            <a href="#75">75</a>
          </li>
        
          <li id="76">
            <a href="#76">76</a>
          </li>
        
          <li id="77">
            <a href="#77">77</a>
          </li>
        
          <li id="78">
            <a href="#78">78</a>
          </li>
        
          <li id="79">
            <a href="#79">79</a>
          </li>
        
          <li id="80">
            <a href="#80">80</a>
          </li>
        
          <li id="81">
            <a href="#81">81</a>
          </li>
        
          <li id="82">
            <a href="#82">82</a>
          </li>
        
      </ul>
      <pre><code><span class="hljs-keyword">import</span> { Agent, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;http&#x27;</span>;
<span class="hljs-keyword">import</span> { Agent <span class="hljs-keyword">as</span> SSLAgent, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;https&#x27;</span>;


<span class="hljs-comment">/**
 * Agent-like object or Agent class or it&#x27;s instance.
 * <span class="hljs-doctag">@global
</span>
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object|Agent|SSLAgent}</span> <span class="hljs-variable">httpAgent</span></span>
 */</span>

<span class="hljs-comment">/**
 * Common nHentai API hosts object.
 * <span class="hljs-doctag">@global
</span>
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">nHentaiHosts</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{?string}</span>         </span>api    Main API host.
 * <span class="hljs-doctag">@property <span class="hljs-type">{?string|string[]}</span> </span>images Media API host(s). Can be a single host or array of hosts for load balancing.
 * <span class="hljs-doctag">@property <span class="hljs-type">{?string|string[]}</span> </span>thumbs Media thumbnails API host(s). Can be a single host or array of hosts for load balancing.
 */</span>

<span class="hljs-comment">/**
 * Common nHentai options object.
 * <span class="hljs-doctag">@global
</span>
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">nHentaiOptions</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{?nHentaiHosts}</span> </span>hosts   Hosts.
 * <span class="hljs-doctag">@property <span class="hljs-type">{?boolean}</span>      </span>ssl     Prefer HTTPS over HTTP.
 * <span class="hljs-doctag">@property <span class="hljs-type">{?httpAgent}</span>    </span>agent   HTTP(S) agent.
 * <span class="hljs-doctag">@property <span class="hljs-type">{?string}</span>       </span>cookies Cookies string in format &#x27;cookie1=value1;cookie2=value2;...&#x27;
 */</span>

<span class="hljs-comment">/**
 * Applies provided options on top of defaults.
 * <span class="hljs-doctag">@param <span class="hljs-type">{?nHentaiOptions}</span> </span>[options={}] Options to apply.
 * <span class="hljs-doctag">@returns <span class="hljs-type">{nHentaiOptions}</span> </span>Unified options.
 */</span>
<span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">processOptions</span>(<span class="hljs-params">{
	hosts: {
		api    = <span class="hljs-string">&#x27;nhentai.net&#x27;</span>,
		images = [
			<span class="hljs-string">&#x27;i1.nhentai.net&#x27;</span>,
			<span class="hljs-string">&#x27;i2.nhentai.net&#x27;</span>,
			<span class="hljs-string">&#x27;i3.nhentai.net&#x27;</span>,
		],
		thumbs = [
			<span class="hljs-string">&#x27;t1.nhentai.net&#x27;</span>,
			<span class="hljs-string">&#x27;t2.nhentai.net&#x27;</span>,
			<span class="hljs-string">&#x27;t3.nhentai.net&#x27;</span>,
		],
	} = {},
	ssl     = <span class="hljs-literal">true</span>,
	agent   = <span class="hljs-literal">null</span>,
	cookies = <span class="hljs-literal">null</span>,
} = {}</span>) </span>{
	<span class="hljs-keyword">if</span> (!agent)
		agent = ssl
			? SSLAgent
			: Agent;

	<span class="hljs-keyword">if</span> (agent.constructor.name === <span class="hljs-string">&#x27;Function&#x27;</span>)
		agent = <span class="hljs-keyword">new</span> agent();

	<span class="hljs-comment">// Normalize hosts to arrays for consistent handling</span>
	<span class="hljs-keyword">const</span> normalizeHosts = <span class="hljs-function">(<span class="hljs-params">hostConfig</span>) =&gt;</span> {
		<span class="hljs-keyword">if</span> (<span class="hljs-keyword">typeof</span> hostConfig === <span class="hljs-string">&#x27;string&#x27;</span>) {
			<span class="hljs-keyword">return</span> [ hostConfig, ];
		}
		<span class="hljs-keyword">return</span> <span class="hljs-built_in">Array</span>.isArray(hostConfig) ? hostConfig : [ hostConfig, ];
	};

	<span class="hljs-keyword">return</span> {
		<span class="hljs-attr">hosts</span>: {
			api,
			<span class="hljs-attr">images</span>: normalizeHosts(images),
			<span class="hljs-attr">thumbs</span>: normalizeHosts(thumbs),
		},
		ssl,
		agent,
		cookies,
	};
}

<span class="hljs-keyword">export</span> <span class="hljs-keyword">default</span> processOptions;
</code></pre>
    </div>
  </div>
</main>
            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
