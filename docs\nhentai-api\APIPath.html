
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: APIPath</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>APIPath</span>
    </div>
    <section class="document__title ">
      <h1>
        APIPath
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class APIPath</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Class used for building URL paths to nHentai API endpoints.
This class is internal and has only static methods.</p></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new APIPath</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"></div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  

  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from APIPath</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#book">static book(bookID: number)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookCover">static bookCover(mediaID: number, extension: string)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookPage">static bookPage(mediaID: number, page: number, extension: string)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookThumb">static bookThumb(mediaID: number, page: number, extension: string)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#randomBookRedirect">static randomBookRedirect()</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#search">static search(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#searchAlike">static searchAlike(bookID: number)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#searchTagged">static searchTagged(tagID: number, page: ?number)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  



  



  



</div>


    
    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="book">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#book">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>book</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#78">
              api.js:78
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static book(bookID: number) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book content endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          bookID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Book ID.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="bookCover">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#bookCover">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>bookCover</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#87">
              api.js:87
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static bookCover(mediaID: number, extension: string) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book's cover image endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          mediaID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Media ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          extension
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        
        <td class="member-parameter__description"><p>Image extension.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="bookPage">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#bookPage">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>bookPage</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#97">
              api.js:97
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static bookPage(mediaID: number, page: number, extension: string) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book's page image endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          mediaID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Media ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          extension
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        
        <td class="member-parameter__description"><p>Image extension.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="bookThumb">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#bookThumb">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>bookThumb</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#108">
              api.js:108
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static bookThumb(mediaID: number, page: number, extension: string) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book's page's thumbnail image endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          mediaID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Media ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          extension
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        
        <td class="member-parameter__description"><p>Image extension.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="randomBookRedirect">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#randomBookRedirect">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>randomBookRedirect</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#119">
              api.js:119
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static randomBookRedirect() → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Redirect to random book at website.</p></div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="search">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#search">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>search</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#48">
              api.js:48
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static search(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search by query endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          query
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Search query.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          ?number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          1
        </td>
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          sort
        </td>
        <td class="member-parameter__type">
          ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ''
        </td>
        <td class="member-parameter__description"><p>Search sort mode.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="searchAlike">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#searchAlike">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>searchAlike</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#69">
              api.js:69
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static searchAlike(bookID: number) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search alike endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          bookID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Book ID.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="searchTagged">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIPath.html#searchTagged">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>searchTagged</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#59">
              api.js:59
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static searchTagged(tagID: number, page: ?number) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search by tag endpoint.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tagID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Tag ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          ?number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          1
        </td>
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>URL path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  
  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#book>book</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookCover>bookCover</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookPage>bookPage</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#bookThumb>bookThumb</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#randomBookRedirect>randomBookRedirect</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#search>search</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#searchAlike>searchAlike</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html#searchTagged>searchTagged</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
