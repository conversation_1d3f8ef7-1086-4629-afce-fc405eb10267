
<!-- Generated by webdoc on 28/05/2025, 10.03.49 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: APIBook</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>APIBook</span>
    </div>
    <section class="document__title ">
      <h1>
        APIBook
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">typedef APIBook</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Book object from API.</p></div>

    

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from APIBook</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html>APIImage</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#cover">static cover</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#id">static id</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html>APIImage</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#images">static images</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#media_id">static media_id</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#num_favorites">static num_favorites</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#num_pages">static num_pages</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#scanlator">static scanlator</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CAPITag.html>APITag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#tags">static tags</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  object
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title">static title</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title">static title</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title">static title</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title">static title</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#uploaded">static uploaded</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="cover">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#cover">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>cover</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static cover: <a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html>APIImage</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book cover image.</p></div>

  

  
  
</div>

  
    
<div class="member" id="id">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#id">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>id</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static id: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="images">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#images">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>images</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static images: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html>APIImage</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book pages' images.</p></div>

  

  
  
</div>

  
    
<div class="member" id="media_id">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#media_id">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>media_id</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static media_id: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book Media ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="num_favorites">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#num_favorites">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>num_favorites</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static num_favorites: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book favours count.</p></div>

  

  
  
</div>

  
    
<div class="member" id="num_pages">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#num_pages">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>num_pages</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static num_pages: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book pages count.</p></div>

  

  
  
</div>

  
    
<div class="member" id="scanlator">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#scanlator">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>scanlator</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static scanlator: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book scanlator.</p></div>

  

  
  
</div>

  
    
<div class="member" id="tags">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#tags">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>tags</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static tags: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CAPITag.html>APITag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="title">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#title">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>title</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static title: object</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book title.</p></div>

  

  
  
</div>

  
    
<div class="member" id="title">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#title">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>title</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static title: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>.english  Book english title.</p></div>

  

  
  
</div>

  
    
<div class="member" id="title">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#title">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>title</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static title: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>.japanese Book japanese title.</p></div>

  

  
  
</div>

  
    
<div class="member" id="title">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#title">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>title</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static title: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>.pretty   Book short title.</p></div>

  

  
  
</div>

  
    
<div class="member" id="uploaded">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIBook.html#uploaded">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>uploaded</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#12">
              book.js:12
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static uploaded: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Upload UNIX timestamp.</p></div>

  

  
  
</div>

  
</div>

    
    
    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#cover>cover</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#id>id</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#images>images</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#media_id>media_id</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#num_favorites>num_favorites</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#num_pages>num_pages</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#scanlator>scanlator</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#tags>tags</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title>title</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title>title</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title>title</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#title>title</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html#uploaded>uploaded</a></li>
    
  </ul>
</div>

  
  
  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
