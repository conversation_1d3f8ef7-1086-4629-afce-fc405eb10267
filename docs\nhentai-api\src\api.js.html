
<!-- Generated by webdoc on 28/05/2025, 10.03.47 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: api.js</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"sources"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs sources">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            

<main class="page-content">
  <div class="source">
    <div class="source__split">
      <ul>
        
          <li id="1">
            <a href="#1">1</a>
          </li>
        
          <li id="2">
            <a href="#2">2</a>
          </li>
        
          <li id="3">
            <a href="#3">3</a>
          </li>
        
          <li id="4">
            <a href="#4">4</a>
          </li>
        
          <li id="5">
            <a href="#5">5</a>
          </li>
        
          <li id="6">
            <a href="#6">6</a>
          </li>
        
          <li id="7">
            <a href="#7">7</a>
          </li>
        
          <li id="8">
            <a href="#8">8</a>
          </li>
        
          <li id="9">
            <a href="#9">9</a>
          </li>
        
          <li id="10">
            <a href="#10">10</a>
          </li>
        
          <li id="11">
            <a href="#11">11</a>
          </li>
        
          <li id="12">
            <a href="#12">12</a>
          </li>
        
          <li id="13">
            <a href="#13">13</a>
          </li>
        
          <li id="14">
            <a href="#14">14</a>
          </li>
        
          <li id="15">
            <a href="#15">15</a>
          </li>
        
          <li id="16">
            <a href="#16">16</a>
          </li>
        
          <li id="17">
            <a href="#17">17</a>
          </li>
        
          <li id="18">
            <a href="#18">18</a>
          </li>
        
          <li id="19">
            <a href="#19">19</a>
          </li>
        
          <li id="20">
            <a href="#20">20</a>
          </li>
        
          <li id="21">
            <a href="#21">21</a>
          </li>
        
          <li id="22">
            <a href="#22">22</a>
          </li>
        
          <li id="23">
            <a href="#23">23</a>
          </li>
        
          <li id="24">
            <a href="#24">24</a>
          </li>
        
          <li id="25">
            <a href="#25">25</a>
          </li>
        
          <li id="26">
            <a href="#26">26</a>
          </li>
        
          <li id="27">
            <a href="#27">27</a>
          </li>
        
          <li id="28">
            <a href="#28">28</a>
          </li>
        
          <li id="29">
            <a href="#29">29</a>
          </li>
        
          <li id="30">
            <a href="#30">30</a>
          </li>
        
          <li id="31">
            <a href="#31">31</a>
          </li>
        
          <li id="32">
            <a href="#32">32</a>
          </li>
        
          <li id="33">
            <a href="#33">33</a>
          </li>
        
          <li id="34">
            <a href="#34">34</a>
          </li>
        
          <li id="35">
            <a href="#35">35</a>
          </li>
        
          <li id="36">
            <a href="#36">36</a>
          </li>
        
          <li id="37">
            <a href="#37">37</a>
          </li>
        
          <li id="38">
            <a href="#38">38</a>
          </li>
        
          <li id="39">
            <a href="#39">39</a>
          </li>
        
          <li id="40">
            <a href="#40">40</a>
          </li>
        
          <li id="41">
            <a href="#41">41</a>
          </li>
        
          <li id="42">
            <a href="#42">42</a>
          </li>
        
          <li id="43">
            <a href="#43">43</a>
          </li>
        
          <li id="44">
            <a href="#44">44</a>
          </li>
        
          <li id="45">
            <a href="#45">45</a>
          </li>
        
          <li id="46">
            <a href="#46">46</a>
          </li>
        
          <li id="47">
            <a href="#47">47</a>
          </li>
        
          <li id="48">
            <a href="#48">48</a>
          </li>
        
          <li id="49">
            <a href="#49">49</a>
          </li>
        
          <li id="50">
            <a href="#50">50</a>
          </li>
        
          <li id="51">
            <a href="#51">51</a>
          </li>
        
          <li id="52">
            <a href="#52">52</a>
          </li>
        
          <li id="53">
            <a href="#53">53</a>
          </li>
        
          <li id="54">
            <a href="#54">54</a>
          </li>
        
          <li id="55">
            <a href="#55">55</a>
          </li>
        
          <li id="56">
            <a href="#56">56</a>
          </li>
        
          <li id="57">
            <a href="#57">57</a>
          </li>
        
          <li id="58">
            <a href="#58">58</a>
          </li>
        
          <li id="59">
            <a href="#59">59</a>
          </li>
        
          <li id="60">
            <a href="#60">60</a>
          </li>
        
          <li id="61">
            <a href="#61">61</a>
          </li>
        
          <li id="62">
            <a href="#62">62</a>
          </li>
        
          <li id="63">
            <a href="#63">63</a>
          </li>
        
          <li id="64">
            <a href="#64">64</a>
          </li>
        
          <li id="65">
            <a href="#65">65</a>
          </li>
        
          <li id="66">
            <a href="#66">66</a>
          </li>
        
          <li id="67">
            <a href="#67">67</a>
          </li>
        
          <li id="68">
            <a href="#68">68</a>
          </li>
        
          <li id="69">
            <a href="#69">69</a>
          </li>
        
          <li id="70">
            <a href="#70">70</a>
          </li>
        
          <li id="71">
            <a href="#71">71</a>
          </li>
        
          <li id="72">
            <a href="#72">72</a>
          </li>
        
          <li id="73">
            <a href="#73">73</a>
          </li>
        
          <li id="74">
            <a href="#74">74</a>
          </li>
        
          <li id="75">
            <a href="#75">75</a>
          </li>
        
          <li id="76">
            <a href="#76">76</a>
          </li>
        
          <li id="77">
            <a href="#77">77</a>
          </li>
        
          <li id="78">
            <a href="#78">78</a>
          </li>
        
          <li id="79">
            <a href="#79">79</a>
          </li>
        
          <li id="80">
            <a href="#80">80</a>
          </li>
        
          <li id="81">
            <a href="#81">81</a>
          </li>
        
          <li id="82">
            <a href="#82">82</a>
          </li>
        
          <li id="83">
            <a href="#83">83</a>
          </li>
        
          <li id="84">
            <a href="#84">84</a>
          </li>
        
          <li id="85">
            <a href="#85">85</a>
          </li>
        
          <li id="86">
            <a href="#86">86</a>
          </li>
        
          <li id="87">
            <a href="#87">87</a>
          </li>
        
          <li id="88">
            <a href="#88">88</a>
          </li>
        
          <li id="89">
            <a href="#89">89</a>
          </li>
        
          <li id="90">
            <a href="#90">90</a>
          </li>
        
          <li id="91">
            <a href="#91">91</a>
          </li>
        
          <li id="92">
            <a href="#92">92</a>
          </li>
        
          <li id="93">
            <a href="#93">93</a>
          </li>
        
          <li id="94">
            <a href="#94">94</a>
          </li>
        
          <li id="95">
            <a href="#95">95</a>
          </li>
        
          <li id="96">
            <a href="#96">96</a>
          </li>
        
          <li id="97">
            <a href="#97">97</a>
          </li>
        
          <li id="98">
            <a href="#98">98</a>
          </li>
        
          <li id="99">
            <a href="#99">99</a>
          </li>
        
          <li id="100">
            <a href="#100">100</a>
          </li>
        
          <li id="101">
            <a href="#101">101</a>
          </li>
        
          <li id="102">
            <a href="#102">102</a>
          </li>
        
          <li id="103">
            <a href="#103">103</a>
          </li>
        
          <li id="104">
            <a href="#104">104</a>
          </li>
        
          <li id="105">
            <a href="#105">105</a>
          </li>
        
          <li id="106">
            <a href="#106">106</a>
          </li>
        
          <li id="107">
            <a href="#107">107</a>
          </li>
        
          <li id="108">
            <a href="#108">108</a>
          </li>
        
          <li id="109">
            <a href="#109">109</a>
          </li>
        
          <li id="110">
            <a href="#110">110</a>
          </li>
        
          <li id="111">
            <a href="#111">111</a>
          </li>
        
          <li id="112">
            <a href="#112">112</a>
          </li>
        
          <li id="113">
            <a href="#113">113</a>
          </li>
        
          <li id="114">
            <a href="#114">114</a>
          </li>
        
          <li id="115">
            <a href="#115">115</a>
          </li>
        
          <li id="116">
            <a href="#116">116</a>
          </li>
        
          <li id="117">
            <a href="#117">117</a>
          </li>
        
          <li id="118">
            <a href="#118">118</a>
          </li>
        
          <li id="119">
            <a href="#119">119</a>
          </li>
        
          <li id="120">
            <a href="#120">120</a>
          </li>
        
          <li id="121">
            <a href="#121">121</a>
          </li>
        
          <li id="122">
            <a href="#122">122</a>
          </li>
        
          <li id="123">
            <a href="#123">123</a>
          </li>
        
          <li id="124">
            <a href="#124">124</a>
          </li>
        
          <li id="125">
            <a href="#125">125</a>
          </li>
        
          <li id="126">
            <a href="#126">126</a>
          </li>
        
          <li id="127">
            <a href="#127">127</a>
          </li>
        
          <li id="128">
            <a href="#128">128</a>
          </li>
        
          <li id="129">
            <a href="#129">129</a>
          </li>
        
          <li id="130">
            <a href="#130">130</a>
          </li>
        
          <li id="131">
            <a href="#131">131</a>
          </li>
        
          <li id="132">
            <a href="#132">132</a>
          </li>
        
          <li id="133">
            <a href="#133">133</a>
          </li>
        
          <li id="134">
            <a href="#134">134</a>
          </li>
        
          <li id="135">
            <a href="#135">135</a>
          </li>
        
          <li id="136">
            <a href="#136">136</a>
          </li>
        
          <li id="137">
            <a href="#137">137</a>
          </li>
        
          <li id="138">
            <a href="#138">138</a>
          </li>
        
          <li id="139">
            <a href="#139">139</a>
          </li>
        
          <li id="140">
            <a href="#140">140</a>
          </li>
        
          <li id="141">
            <a href="#141">141</a>
          </li>
        
          <li id="142">
            <a href="#142">142</a>
          </li>
        
          <li id="143">
            <a href="#143">143</a>
          </li>
        
          <li id="144">
            <a href="#144">144</a>
          </li>
        
          <li id="145">
            <a href="#145">145</a>
          </li>
        
          <li id="146">
            <a href="#146">146</a>
          </li>
        
          <li id="147">
            <a href="#147">147</a>
          </li>
        
          <li id="148">
            <a href="#148">148</a>
          </li>
        
          <li id="149">
            <a href="#149">149</a>
          </li>
        
          <li id="150">
            <a href="#150">150</a>
          </li>
        
          <li id="151">
            <a href="#151">151</a>
          </li>
        
          <li id="152">
            <a href="#152">152</a>
          </li>
        
          <li id="153">
            <a href="#153">153</a>
          </li>
        
          <li id="154">
            <a href="#154">154</a>
          </li>
        
          <li id="155">
            <a href="#155">155</a>
          </li>
        
          <li id="156">
            <a href="#156">156</a>
          </li>
        
          <li id="157">
            <a href="#157">157</a>
          </li>
        
          <li id="158">
            <a href="#158">158</a>
          </li>
        
          <li id="159">
            <a href="#159">159</a>
          </li>
        
          <li id="160">
            <a href="#160">160</a>
          </li>
        
          <li id="161">
            <a href="#161">161</a>
          </li>
        
          <li id="162">
            <a href="#162">162</a>
          </li>
        
          <li id="163">
            <a href="#163">163</a>
          </li>
        
          <li id="164">
            <a href="#164">164</a>
          </li>
        
          <li id="165">
            <a href="#165">165</a>
          </li>
        
          <li id="166">
            <a href="#166">166</a>
          </li>
        
          <li id="167">
            <a href="#167">167</a>
          </li>
        
          <li id="168">
            <a href="#168">168</a>
          </li>
        
          <li id="169">
            <a href="#169">169</a>
          </li>
        
          <li id="170">
            <a href="#170">170</a>
          </li>
        
          <li id="171">
            <a href="#171">171</a>
          </li>
        
          <li id="172">
            <a href="#172">172</a>
          </li>
        
          <li id="173">
            <a href="#173">173</a>
          </li>
        
          <li id="174">
            <a href="#174">174</a>
          </li>
        
          <li id="175">
            <a href="#175">175</a>
          </li>
        
          <li id="176">
            <a href="#176">176</a>
          </li>
        
          <li id="177">
            <a href="#177">177</a>
          </li>
        
          <li id="178">
            <a href="#178">178</a>
          </li>
        
          <li id="179">
            <a href="#179">179</a>
          </li>
        
          <li id="180">
            <a href="#180">180</a>
          </li>
        
          <li id="181">
            <a href="#181">181</a>
          </li>
        
          <li id="182">
            <a href="#182">182</a>
          </li>
        
          <li id="183">
            <a href="#183">183</a>
          </li>
        
          <li id="184">
            <a href="#184">184</a>
          </li>
        
          <li id="185">
            <a href="#185">185</a>
          </li>
        
          <li id="186">
            <a href="#186">186</a>
          </li>
        
          <li id="187">
            <a href="#187">187</a>
          </li>
        
          <li id="188">
            <a href="#188">188</a>
          </li>
        
          <li id="189">
            <a href="#189">189</a>
          </li>
        
          <li id="190">
            <a href="#190">190</a>
          </li>
        
          <li id="191">
            <a href="#191">191</a>
          </li>
        
          <li id="192">
            <a href="#192">192</a>
          </li>
        
          <li id="193">
            <a href="#193">193</a>
          </li>
        
          <li id="194">
            <a href="#194">194</a>
          </li>
        
          <li id="195">
            <a href="#195">195</a>
          </li>
        
          <li id="196">
            <a href="#196">196</a>
          </li>
        
          <li id="197">
            <a href="#197">197</a>
          </li>
        
          <li id="198">
            <a href="#198">198</a>
          </li>
        
          <li id="199">
            <a href="#199">199</a>
          </li>
        
          <li id="200">
            <a href="#200">200</a>
          </li>
        
          <li id="201">
            <a href="#201">201</a>
          </li>
        
          <li id="202">
            <a href="#202">202</a>
          </li>
        
          <li id="203">
            <a href="#203">203</a>
          </li>
        
          <li id="204">
            <a href="#204">204</a>
          </li>
        
          <li id="205">
            <a href="#205">205</a>
          </li>
        
          <li id="206">
            <a href="#206">206</a>
          </li>
        
          <li id="207">
            <a href="#207">207</a>
          </li>
        
          <li id="208">
            <a href="#208">208</a>
          </li>
        
          <li id="209">
            <a href="#209">209</a>
          </li>
        
          <li id="210">
            <a href="#210">210</a>
          </li>
        
          <li id="211">
            <a href="#211">211</a>
          </li>
        
          <li id="212">
            <a href="#212">212</a>
          </li>
        
          <li id="213">
            <a href="#213">213</a>
          </li>
        
          <li id="214">
            <a href="#214">214</a>
          </li>
        
          <li id="215">
            <a href="#215">215</a>
          </li>
        
          <li id="216">
            <a href="#216">216</a>
          </li>
        
          <li id="217">
            <a href="#217">217</a>
          </li>
        
          <li id="218">
            <a href="#218">218</a>
          </li>
        
          <li id="219">
            <a href="#219">219</a>
          </li>
        
          <li id="220">
            <a href="#220">220</a>
          </li>
        
          <li id="221">
            <a href="#221">221</a>
          </li>
        
          <li id="222">
            <a href="#222">222</a>
          </li>
        
          <li id="223">
            <a href="#223">223</a>
          </li>
        
          <li id="224">
            <a href="#224">224</a>
          </li>
        
          <li id="225">
            <a href="#225">225</a>
          </li>
        
          <li id="226">
            <a href="#226">226</a>
          </li>
        
          <li id="227">
            <a href="#227">227</a>
          </li>
        
          <li id="228">
            <a href="#228">228</a>
          </li>
        
          <li id="229">
            <a href="#229">229</a>
          </li>
        
          <li id="230">
            <a href="#230">230</a>
          </li>
        
          <li id="231">
            <a href="#231">231</a>
          </li>
        
          <li id="232">
            <a href="#232">232</a>
          </li>
        
          <li id="233">
            <a href="#233">233</a>
          </li>
        
          <li id="234">
            <a href="#234">234</a>
          </li>
        
          <li id="235">
            <a href="#235">235</a>
          </li>
        
          <li id="236">
            <a href="#236">236</a>
          </li>
        
          <li id="237">
            <a href="#237">237</a>
          </li>
        
          <li id="238">
            <a href="#238">238</a>
          </li>
        
          <li id="239">
            <a href="#239">239</a>
          </li>
        
          <li id="240">
            <a href="#240">240</a>
          </li>
        
          <li id="241">
            <a href="#241">241</a>
          </li>
        
          <li id="242">
            <a href="#242">242</a>
          </li>
        
          <li id="243">
            <a href="#243">243</a>
          </li>
        
          <li id="244">
            <a href="#244">244</a>
          </li>
        
          <li id="245">
            <a href="#245">245</a>
          </li>
        
          <li id="246">
            <a href="#246">246</a>
          </li>
        
          <li id="247">
            <a href="#247">247</a>
          </li>
        
          <li id="248">
            <a href="#248">248</a>
          </li>
        
          <li id="249">
            <a href="#249">249</a>
          </li>
        
          <li id="250">
            <a href="#250">250</a>
          </li>
        
          <li id="251">
            <a href="#251">251</a>
          </li>
        
          <li id="252">
            <a href="#252">252</a>
          </li>
        
          <li id="253">
            <a href="#253">253</a>
          </li>
        
          <li id="254">
            <a href="#254">254</a>
          </li>
        
          <li id="255">
            <a href="#255">255</a>
          </li>
        
          <li id="256">
            <a href="#256">256</a>
          </li>
        
          <li id="257">
            <a href="#257">257</a>
          </li>
        
          <li id="258">
            <a href="#258">258</a>
          </li>
        
          <li id="259">
            <a href="#259">259</a>
          </li>
        
          <li id="260">
            <a href="#260">260</a>
          </li>
        
          <li id="261">
            <a href="#261">261</a>
          </li>
        
          <li id="262">
            <a href="#262">262</a>
          </li>
        
          <li id="263">
            <a href="#263">263</a>
          </li>
        
          <li id="264">
            <a href="#264">264</a>
          </li>
        
          <li id="265">
            <a href="#265">265</a>
          </li>
        
          <li id="266">
            <a href="#266">266</a>
          </li>
        
          <li id="267">
            <a href="#267">267</a>
          </li>
        
          <li id="268">
            <a href="#268">268</a>
          </li>
        
          <li id="269">
            <a href="#269">269</a>
          </li>
        
          <li id="270">
            <a href="#270">270</a>
          </li>
        
          <li id="271">
            <a href="#271">271</a>
          </li>
        
          <li id="272">
            <a href="#272">272</a>
          </li>
        
          <li id="273">
            <a href="#273">273</a>
          </li>
        
          <li id="274">
            <a href="#274">274</a>
          </li>
        
          <li id="275">
            <a href="#275">275</a>
          </li>
        
          <li id="276">
            <a href="#276">276</a>
          </li>
        
          <li id="277">
            <a href="#277">277</a>
          </li>
        
          <li id="278">
            <a href="#278">278</a>
          </li>
        
          <li id="279">
            <a href="#279">279</a>
          </li>
        
          <li id="280">
            <a href="#280">280</a>
          </li>
        
          <li id="281">
            <a href="#281">281</a>
          </li>
        
          <li id="282">
            <a href="#282">282</a>
          </li>
        
          <li id="283">
            <a href="#283">283</a>
          </li>
        
          <li id="284">
            <a href="#284">284</a>
          </li>
        
          <li id="285">
            <a href="#285">285</a>
          </li>
        
          <li id="286">
            <a href="#286">286</a>
          </li>
        
          <li id="287">
            <a href="#287">287</a>
          </li>
        
          <li id="288">
            <a href="#288">288</a>
          </li>
        
          <li id="289">
            <a href="#289">289</a>
          </li>
        
          <li id="290">
            <a href="#290">290</a>
          </li>
        
          <li id="291">
            <a href="#291">291</a>
          </li>
        
          <li id="292">
            <a href="#292">292</a>
          </li>
        
          <li id="293">
            <a href="#293">293</a>
          </li>
        
          <li id="294">
            <a href="#294">294</a>
          </li>
        
          <li id="295">
            <a href="#295">295</a>
          </li>
        
          <li id="296">
            <a href="#296">296</a>
          </li>
        
          <li id="297">
            <a href="#297">297</a>
          </li>
        
          <li id="298">
            <a href="#298">298</a>
          </li>
        
          <li id="299">
            <a href="#299">299</a>
          </li>
        
          <li id="300">
            <a href="#300">300</a>
          </li>
        
          <li id="301">
            <a href="#301">301</a>
          </li>
        
          <li id="302">
            <a href="#302">302</a>
          </li>
        
          <li id="303">
            <a href="#303">303</a>
          </li>
        
          <li id="304">
            <a href="#304">304</a>
          </li>
        
          <li id="305">
            <a href="#305">305</a>
          </li>
        
          <li id="306">
            <a href="#306">306</a>
          </li>
        
          <li id="307">
            <a href="#307">307</a>
          </li>
        
          <li id="308">
            <a href="#308">308</a>
          </li>
        
          <li id="309">
            <a href="#309">309</a>
          </li>
        
          <li id="310">
            <a href="#310">310</a>
          </li>
        
          <li id="311">
            <a href="#311">311</a>
          </li>
        
          <li id="312">
            <a href="#312">312</a>
          </li>
        
          <li id="313">
            <a href="#313">313</a>
          </li>
        
          <li id="314">
            <a href="#314">314</a>
          </li>
        
          <li id="315">
            <a href="#315">315</a>
          </li>
        
          <li id="316">
            <a href="#316">316</a>
          </li>
        
          <li id="317">
            <a href="#317">317</a>
          </li>
        
          <li id="318">
            <a href="#318">318</a>
          </li>
        
          <li id="319">
            <a href="#319">319</a>
          </li>
        
          <li id="320">
            <a href="#320">320</a>
          </li>
        
          <li id="321">
            <a href="#321">321</a>
          </li>
        
          <li id="322">
            <a href="#322">322</a>
          </li>
        
          <li id="323">
            <a href="#323">323</a>
          </li>
        
          <li id="324">
            <a href="#324">324</a>
          </li>
        
          <li id="325">
            <a href="#325">325</a>
          </li>
        
          <li id="326">
            <a href="#326">326</a>
          </li>
        
          <li id="327">
            <a href="#327">327</a>
          </li>
        
          <li id="328">
            <a href="#328">328</a>
          </li>
        
          <li id="329">
            <a href="#329">329</a>
          </li>
        
          <li id="330">
            <a href="#330">330</a>
          </li>
        
          <li id="331">
            <a href="#331">331</a>
          </li>
        
          <li id="332">
            <a href="#332">332</a>
          </li>
        
          <li id="333">
            <a href="#333">333</a>
          </li>
        
          <li id="334">
            <a href="#334">334</a>
          </li>
        
          <li id="335">
            <a href="#335">335</a>
          </li>
        
          <li id="336">
            <a href="#336">336</a>
          </li>
        
          <li id="337">
            <a href="#337">337</a>
          </li>
        
          <li id="338">
            <a href="#338">338</a>
          </li>
        
          <li id="339">
            <a href="#339">339</a>
          </li>
        
          <li id="340">
            <a href="#340">340</a>
          </li>
        
          <li id="341">
            <a href="#341">341</a>
          </li>
        
          <li id="342">
            <a href="#342">342</a>
          </li>
        
          <li id="343">
            <a href="#343">343</a>
          </li>
        
          <li id="344">
            <a href="#344">344</a>
          </li>
        
          <li id="345">
            <a href="#345">345</a>
          </li>
        
          <li id="346">
            <a href="#346">346</a>
          </li>
        
          <li id="347">
            <a href="#347">347</a>
          </li>
        
          <li id="348">
            <a href="#348">348</a>
          </li>
        
          <li id="349">
            <a href="#349">349</a>
          </li>
        
          <li id="350">
            <a href="#350">350</a>
          </li>
        
          <li id="351">
            <a href="#351">351</a>
          </li>
        
          <li id="352">
            <a href="#352">352</a>
          </li>
        
          <li id="353">
            <a href="#353">353</a>
          </li>
        
          <li id="354">
            <a href="#354">354</a>
          </li>
        
          <li id="355">
            <a href="#355">355</a>
          </li>
        
          <li id="356">
            <a href="#356">356</a>
          </li>
        
          <li id="357">
            <a href="#357">357</a>
          </li>
        
          <li id="358">
            <a href="#358">358</a>
          </li>
        
          <li id="359">
            <a href="#359">359</a>
          </li>
        
          <li id="360">
            <a href="#360">360</a>
          </li>
        
          <li id="361">
            <a href="#361">361</a>
          </li>
        
          <li id="362">
            <a href="#362">362</a>
          </li>
        
          <li id="363">
            <a href="#363">363</a>
          </li>
        
          <li id="364">
            <a href="#364">364</a>
          </li>
        
          <li id="365">
            <a href="#365">365</a>
          </li>
        
          <li id="366">
            <a href="#366">366</a>
          </li>
        
          <li id="367">
            <a href="#367">367</a>
          </li>
        
          <li id="368">
            <a href="#368">368</a>
          </li>
        
          <li id="369">
            <a href="#369">369</a>
          </li>
        
          <li id="370">
            <a href="#370">370</a>
          </li>
        
          <li id="371">
            <a href="#371">371</a>
          </li>
        
          <li id="372">
            <a href="#372">372</a>
          </li>
        
          <li id="373">
            <a href="#373">373</a>
          </li>
        
          <li id="374">
            <a href="#374">374</a>
          </li>
        
          <li id="375">
            <a href="#375">375</a>
          </li>
        
          <li id="376">
            <a href="#376">376</a>
          </li>
        
          <li id="377">
            <a href="#377">377</a>
          </li>
        
          <li id="378">
            <a href="#378">378</a>
          </li>
        
          <li id="379">
            <a href="#379">379</a>
          </li>
        
          <li id="380">
            <a href="#380">380</a>
          </li>
        
          <li id="381">
            <a href="#381">381</a>
          </li>
        
          <li id="382">
            <a href="#382">382</a>
          </li>
        
          <li id="383">
            <a href="#383">383</a>
          </li>
        
          <li id="384">
            <a href="#384">384</a>
          </li>
        
          <li id="385">
            <a href="#385">385</a>
          </li>
        
          <li id="386">
            <a href="#386">386</a>
          </li>
        
          <li id="387">
            <a href="#387">387</a>
          </li>
        
          <li id="388">
            <a href="#388">388</a>
          </li>
        
          <li id="389">
            <a href="#389">389</a>
          </li>
        
          <li id="390">
            <a href="#390">390</a>
          </li>
        
          <li id="391">
            <a href="#391">391</a>
          </li>
        
          <li id="392">
            <a href="#392">392</a>
          </li>
        
          <li id="393">
            <a href="#393">393</a>
          </li>
        
          <li id="394">
            <a href="#394">394</a>
          </li>
        
          <li id="395">
            <a href="#395">395</a>
          </li>
        
          <li id="396">
            <a href="#396">396</a>
          </li>
        
          <li id="397">
            <a href="#397">397</a>
          </li>
        
          <li id="398">
            <a href="#398">398</a>
          </li>
        
          <li id="399">
            <a href="#399">399</a>
          </li>
        
          <li id="400">
            <a href="#400">400</a>
          </li>
        
          <li id="401">
            <a href="#401">401</a>
          </li>
        
          <li id="402">
            <a href="#402">402</a>
          </li>
        
          <li id="403">
            <a href="#403">403</a>
          </li>
        
          <li id="404">
            <a href="#404">404</a>
          </li>
        
          <li id="405">
            <a href="#405">405</a>
          </li>
        
          <li id="406">
            <a href="#406">406</a>
          </li>
        
          <li id="407">
            <a href="#407">407</a>
          </li>
        
          <li id="408">
            <a href="#408">408</a>
          </li>
        
          <li id="409">
            <a href="#409">409</a>
          </li>
        
          <li id="410">
            <a href="#410">410</a>
          </li>
        
          <li id="411">
            <a href="#411">411</a>
          </li>
        
          <li id="412">
            <a href="#412">412</a>
          </li>
        
          <li id="413">
            <a href="#413">413</a>
          </li>
        
          <li id="414">
            <a href="#414">414</a>
          </li>
        
          <li id="415">
            <a href="#415">415</a>
          </li>
        
          <li id="416">
            <a href="#416">416</a>
          </li>
        
          <li id="417">
            <a href="#417">417</a>
          </li>
        
          <li id="418">
            <a href="#418">418</a>
          </li>
        
          <li id="419">
            <a href="#419">419</a>
          </li>
        
          <li id="420">
            <a href="#420">420</a>
          </li>
        
          <li id="421">
            <a href="#421">421</a>
          </li>
        
          <li id="422">
            <a href="#422">422</a>
          </li>
        
          <li id="423">
            <a href="#423">423</a>
          </li>
        
          <li id="424">
            <a href="#424">424</a>
          </li>
        
          <li id="425">
            <a href="#425">425</a>
          </li>
        
          <li id="426">
            <a href="#426">426</a>
          </li>
        
          <li id="427">
            <a href="#427">427</a>
          </li>
        
          <li id="428">
            <a href="#428">428</a>
          </li>
        
          <li id="429">
            <a href="#429">429</a>
          </li>
        
          <li id="430">
            <a href="#430">430</a>
          </li>
        
          <li id="431">
            <a href="#431">431</a>
          </li>
        
          <li id="432">
            <a href="#432">432</a>
          </li>
        
          <li id="433">
            <a href="#433">433</a>
          </li>
        
          <li id="434">
            <a href="#434">434</a>
          </li>
        
          <li id="435">
            <a href="#435">435</a>
          </li>
        
          <li id="436">
            <a href="#436">436</a>
          </li>
        
          <li id="437">
            <a href="#437">437</a>
          </li>
        
          <li id="438">
            <a href="#438">438</a>
          </li>
        
          <li id="439">
            <a href="#439">439</a>
          </li>
        
          <li id="440">
            <a href="#440">440</a>
          </li>
        
          <li id="441">
            <a href="#441">441</a>
          </li>
        
          <li id="442">
            <a href="#442">442</a>
          </li>
        
          <li id="443">
            <a href="#443">443</a>
          </li>
        
          <li id="444">
            <a href="#444">444</a>
          </li>
        
          <li id="445">
            <a href="#445">445</a>
          </li>
        
          <li id="446">
            <a href="#446">446</a>
          </li>
        
          <li id="447">
            <a href="#447">447</a>
          </li>
        
          <li id="448">
            <a href="#448">448</a>
          </li>
        
          <li id="449">
            <a href="#449">449</a>
          </li>
        
          <li id="450">
            <a href="#450">450</a>
          </li>
        
          <li id="451">
            <a href="#451">451</a>
          </li>
        
          <li id="452">
            <a href="#452">452</a>
          </li>
        
          <li id="453">
            <a href="#453">453</a>
          </li>
        
          <li id="454">
            <a href="#454">454</a>
          </li>
        
          <li id="455">
            <a href="#455">455</a>
          </li>
        
          <li id="456">
            <a href="#456">456</a>
          </li>
        
          <li id="457">
            <a href="#457">457</a>
          </li>
        
          <li id="458">
            <a href="#458">458</a>
          </li>
        
          <li id="459">
            <a href="#459">459</a>
          </li>
        
          <li id="460">
            <a href="#460">460</a>
          </li>
        
          <li id="461">
            <a href="#461">461</a>
          </li>
        
          <li id="462">
            <a href="#462">462</a>
          </li>
        
          <li id="463">
            <a href="#463">463</a>
          </li>
        
          <li id="464">
            <a href="#464">464</a>
          </li>
        
          <li id="465">
            <a href="#465">465</a>
          </li>
        
          <li id="466">
            <a href="#466">466</a>
          </li>
        
          <li id="467">
            <a href="#467">467</a>
          </li>
        
          <li id="468">
            <a href="#468">468</a>
          </li>
        
          <li id="469">
            <a href="#469">469</a>
          </li>
        
          <li id="470">
            <a href="#470">470</a>
          </li>
        
          <li id="471">
            <a href="#471">471</a>
          </li>
        
          <li id="472">
            <a href="#472">472</a>
          </li>
        
          <li id="473">
            <a href="#473">473</a>
          </li>
        
          <li id="474">
            <a href="#474">474</a>
          </li>
        
          <li id="475">
            <a href="#475">475</a>
          </li>
        
          <li id="476">
            <a href="#476">476</a>
          </li>
        
          <li id="477">
            <a href="#477">477</a>
          </li>
        
          <li id="478">
            <a href="#478">478</a>
          </li>
        
          <li id="479">
            <a href="#479">479</a>
          </li>
        
          <li id="480">
            <a href="#480">480</a>
          </li>
        
          <li id="481">
            <a href="#481">481</a>
          </li>
        
          <li id="482">
            <a href="#482">482</a>
          </li>
        
          <li id="483">
            <a href="#483">483</a>
          </li>
        
          <li id="484">
            <a href="#484">484</a>
          </li>
        
          <li id="485">
            <a href="#485">485</a>
          </li>
        
          <li id="486">
            <a href="#486">486</a>
          </li>
        
          <li id="487">
            <a href="#487">487</a>
          </li>
        
          <li id="488">
            <a href="#488">488</a>
          </li>
        
          <li id="489">
            <a href="#489">489</a>
          </li>
        
          <li id="490">
            <a href="#490">490</a>
          </li>
        
          <li id="491">
            <a href="#491">491</a>
          </li>
        
          <li id="492">
            <a href="#492">492</a>
          </li>
        
          <li id="493">
            <a href="#493">493</a>
          </li>
        
          <li id="494">
            <a href="#494">494</a>
          </li>
        
          <li id="495">
            <a href="#495">495</a>
          </li>
        
          <li id="496">
            <a href="#496">496</a>
          </li>
        
          <li id="497">
            <a href="#497">497</a>
          </li>
        
          <li id="498">
            <a href="#498">498</a>
          </li>
        
          <li id="499">
            <a href="#499">499</a>
          </li>
        
      </ul>
      <pre><code><span class="hljs-comment">/**
 * <span class="hljs-doctag">@module <span class="hljs-variable">API</span></span>
 */</span>

<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{ import(&quot;./options&quot;).nHentaiOptions }</span> <span class="hljs-variable">nHentaiOptions</span></span>
 */</span>

<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{ import(&quot;./options&quot;).nHentaiHosts }</span> <span class="hljs-variable">nHentaiHosts</span></span>
 */</span>

<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{ import(&quot;./options&quot;).httpAgent }</span> <span class="hljs-variable">httpAgent</span></span>
 */</span>

<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{ import(&quot;./search&quot;).SearchSortMode }</span> <span class="hljs-variable">SearchSortMode</span></span>
 */</span>

<span class="hljs-comment">// eslint-disable-next-line no-unused-vars</span>
<span class="hljs-keyword">import</span> http, { IncomingMessage, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;http&#x27;</span>;
<span class="hljs-keyword">import</span> https <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;https&#x27;</span>;

<span class="hljs-keyword">import</span> { version, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;../package.json&#x27;</span>;

<span class="hljs-keyword">import</span> Book <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./book&#x27;</span>;
<span class="hljs-keyword">import</span> APIError <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./error&#x27;</span>;
<span class="hljs-keyword">import</span> Image <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./image&#x27;</span>;
<span class="hljs-keyword">import</span> processOptions <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./options&#x27;</span>;
<span class="hljs-keyword">import</span> { Search, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./search&#x27;</span>;
<span class="hljs-keyword">import</span> { Tag, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./tag&#x27;</span>;


<span class="hljs-comment">/**
 * API arguments
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">APIArgs</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{string}</span>   </span>host    API host.
 * <span class="hljs-doctag">@property <span class="hljs-type">{Function}</span> </span>apiPath API endpoint URL path generator.
 */</span>

<span class="hljs-comment">/**
 * Class used for building URL paths to nHentai API endpoints.
 * This class is internal and has only static methods.
 * <span class="hljs-doctag">@class
</span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">APIPath</span> </span>{
	<span class="hljs-comment">/**
	 * Search by query endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span>          </span>query     Search query.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[page=1]  Page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?SearchSortMode}</span> </span>[sort=&#x27;&#x27;] Search sort mode.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">search</span>(<span class="hljs-params">query, page = <span class="hljs-number">1</span>, sort = <span class="hljs-string">&#x27;&#x27;</span></span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/api/galleries/search?query=<span class="hljs-subst">${query}</span>&amp;page=<span class="hljs-subst">${page}</span><span class="hljs-subst">${sort ? <span class="hljs-string">&#x27;&amp;sort=&#x27;</span> + sort : <span class="hljs-string">&#x27;&#x27;</span>}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Search by tag endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span>  </span>tagID    Tag ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span> </span>[page=1] Page ID.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">searchTagged</span>(<span class="hljs-params">tagID, page = <span class="hljs-number">1</span></span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/api/galleries/tagged?tag_id=<span class="hljs-subst">${tagID}</span>&amp;page=<span class="hljs-subst">${page}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Search alike endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>bookID Book ID.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">searchAlike</span>(<span class="hljs-params">bookID</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/api/gallery/<span class="hljs-subst">${bookID}</span>/related`</span>;
	}

	<span class="hljs-comment">/**
	 * Book content endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>bookID Book ID.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">book</span>(<span class="hljs-params">bookID</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/api/gallery/<span class="hljs-subst">${bookID}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Book&#x27;s cover image endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>mediaID   Media ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>extension Image extension.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">bookCover</span>(<span class="hljs-params">mediaID, extension</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/galleries/<span class="hljs-subst">${mediaID}</span>/cover.<span class="hljs-subst">${extension}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Book&#x27;s page image endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>mediaID   Media ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>page      Page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>extension Image extension.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">bookPage</span>(<span class="hljs-params">mediaID, page, extension</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/galleries/<span class="hljs-subst">${mediaID}</span>/<span class="hljs-subst">${page}</span>.<span class="hljs-subst">${extension}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Book&#x27;s page&#x27;s thumbnail image endpoint.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>mediaID   Media ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>page      Page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>extension Image extension.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">bookThumb</span>(<span class="hljs-params">mediaID, page, extension</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`/galleries/<span class="hljs-subst">${mediaID}</span>/<span class="hljs-subst">${page}</span>t.<span class="hljs-subst">${extension}</span>`</span>;
	}

	<span class="hljs-comment">/**
	 * Redirect to random book at website.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>URL path.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">randomBookRedirect</span>(<span class="hljs-params"></span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-string">&#x27;/random/&#x27;</span>;
	}
}

<span class="hljs-comment">/**
 * Class used for interaction with nHentai API.
 * <span class="hljs-doctag">@class
</span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">API</span> </span>{
	<span class="hljs-comment">/**
	 * API path class
	 * <span class="hljs-doctag">@type <span class="hljs-type">{APIPath}</span>
</span>
	 * <span class="hljs-doctag">@static
</span>
	 * <span class="hljs-doctag">@private
</span>
	 */</span>
	<span class="hljs-keyword">static</span> APIPath = APIPath;

	<span class="hljs-comment">/**
	 * Hosts
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?nHentaiHosts}</span>
</span>
	 */</span>
	hosts;

	<span class="hljs-comment">/**
	 * Prefer HTTPS over HTTP.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?boolean}</span>
</span>
	 */</span>
	ssl;

	<span class="hljs-comment">/**
	 * HTTP(S) agent.
	 * <span class="hljs-doctag">@property <span class="hljs-type">{?httpAgent}</span>
</span>
	 */</span>
	agent;

	<span class="hljs-comment">/**
	 * Cookies string.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?string}</span>
</span>
	 */</span>
	cookies;

	<span class="hljs-comment">/**
	 * Applies provided options on top of defaults.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?nHentaiOptions}</span> </span>[options={}] Options to apply.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params">options = {}</span>)</span> {
		<span class="hljs-keyword">let</span> params = processOptions(options);

		<span class="hljs-built_in">Object</span>.assign(<span class="hljs-built_in">this</span>, params);
	}

	<span class="hljs-comment">/**
	 * Get http(s) module depending on `options.ssl`.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{https|http}</span>
</span>
	 */</span>
	<span class="hljs-keyword">get</span> <span class="hljs-title">net</span>() {
		<span class="hljs-keyword">return</span> <span class="hljs-built_in">this</span>.ssl
			? https
			: http;
	}

	<span class="hljs-comment">/**
	 * Select a host from an array of hosts using round-robin.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string[]}</span> </span>hosts Array of hosts.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>[fallback] Fallback host if array is empty.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>Selected host.
	 * <span class="hljs-doctag">@private
</span>
	 */</span>
	<span class="hljs-function"><span class="hljs-title">selectHost</span>(<span class="hljs-params">hosts, fallback = <span class="hljs-string">&#x27;nhentai.net&#x27;</span></span>)</span> {
		<span class="hljs-keyword">if</span> (!<span class="hljs-built_in">Array</span>.isArray(hosts) || hosts.length === <span class="hljs-number">0</span>) {
			<span class="hljs-keyword">return</span> fallback;
		}

		<span class="hljs-comment">// Simple round-robin selection based on current time</span>
		<span class="hljs-keyword">const</span> index = <span class="hljs-built_in">Math</span>.floor(<span class="hljs-built_in">Math</span>.random() * hosts.length);
		<span class="hljs-keyword">return</span> hosts[index];
	}

	<span class="hljs-comment">/**
	 * JSON get request.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{object}</span> </span>options      HTTP(S) request options.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>options.host Host.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>options.path Path.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;object&gt;}</span> </span>Parsed JSON.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">request</span>(<span class="hljs-params">options</span>)</span> {
		<span class="hljs-keyword">let</span> {
			net,
			agent,
			cookies,
		} = <span class="hljs-built_in">this</span>;
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Promise</span>(<span class="hljs-function">(<span class="hljs-params">resolve, reject</span>) =&gt;</span> {
			<span class="hljs-keyword">const</span> headers = {
				<span class="hljs-string">&#x27;User-Agent&#x27;</span>: <span class="hljs-string">`nhentai-api-client/<span class="hljs-subst">${version}</span> Node.js/<span class="hljs-subst">${process.versions.node}</span>`</span>,
			};

			<span class="hljs-comment">// Add cookies if provided</span>
			<span class="hljs-keyword">if</span> (cookies) {
				headers.Cookie = cookies;
			}

			<span class="hljs-built_in">Object</span>.assign(options, {
				agent,
				headers,
			});

			net.get(options, <span class="hljs-function"><span class="hljs-params">_response</span> =&gt;</span> {
				<span class="hljs-keyword">const</span>
					<span class="hljs-comment">/** <span class="hljs-doctag">@type <span class="hljs-type">{IncomingMessage}</span></span>*/</span>
					response = _response,
					{ statusCode, } = response,
					contentType = response.headers[<span class="hljs-string">&#x27;content-type&#x27;</span>];

				<span class="hljs-keyword">let</span> error;
				<span class="hljs-keyword">if</span> (statusCode !== <span class="hljs-number">200</span>)
					error = <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">`Request failed with status code <span class="hljs-subst">${statusCode}</span>`</span>);
				<span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (!(<span class="hljs-regexp">/^application\/json/</span>).test(contentType))
					error = <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">`Invalid content-type - expected application/json but received <span class="hljs-subst">${contentType}</span>`</span>);

				<span class="hljs-keyword">if</span> (error) {
					response.resume();
					reject(APIError.absorb(error, response));
					<span class="hljs-keyword">return</span>;
				}

				response.setEncoding(<span class="hljs-string">&#x27;utf8&#x27;</span>);
				<span class="hljs-keyword">let</span> rawData = <span class="hljs-string">&#x27;&#x27;</span>;
				response.on(<span class="hljs-string">&#x27;data&#x27;</span>, <span class="hljs-function">(<span class="hljs-params">chunk</span>) =&gt;</span> rawData += chunk);
				response.on(<span class="hljs-string">&#x27;end&#x27;</span>, <span class="hljs-function">() =&gt;</span> {
					<span class="hljs-keyword">try</span> {
						resolve(<span class="hljs-built_in">JSON</span>.parse(rawData));
					} <span class="hljs-keyword">catch</span> (error) {
						reject(APIError.absorb(error, response));
					}
				});
			}).on(<span class="hljs-string">&#x27;error&#x27;</span>, <span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> reject(APIError.absorb(error)));
		});
	}

	<span class="hljs-comment">/**
	 * Get API arguments.
	 * This is internal method.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>hostType Host type.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>api      Endpoint type.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{APIArgs}</span> </span>API arguments.
	 * <span class="hljs-doctag">@private
</span>
	 */</span>
	<span class="hljs-function"><span class="hljs-title">getAPIArgs</span>(<span class="hljs-params">hostType, api</span>)</span> {
		<span class="hljs-keyword">let</span> {
			<span class="hljs-attr">hosts</span>: {
				[hostType]: hostConfig,
			},
			<span class="hljs-attr">constructor</span>: {
				<span class="hljs-attr">APIPath</span>: {
					[api]: apiPath,
				},
			},
		} = <span class="hljs-built_in">this</span>;

		<span class="hljs-comment">// Select host from array or use single host</span>
		<span class="hljs-keyword">const</span> host = <span class="hljs-built_in">Array</span>.isArray(hostConfig)
			? <span class="hljs-built_in">this</span>.selectHost(hostConfig, hostConfig[<span class="hljs-number">0</span>])
			: hostConfig;

		<span class="hljs-keyword">return</span> {
			host,
			apiPath,
		};
	}

	<span class="hljs-comment">/**
	 * Search by query.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span>          </span>query     Query.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[page=1]  Page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?SearchSortMode}</span> </span>[sort=&#x27;&#x27;] Search sort mode.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Search&gt;}</span> </span>Search instance.
	 * <span class="hljs-doctag">@async
</span>
	 */</span>
	<span class="hljs-keyword">async</span> <span class="hljs-function"><span class="hljs-title">search</span>(<span class="hljs-params">query, page = <span class="hljs-number">1</span>, sort = <span class="hljs-string">&#x27;&#x27;</span></span>)</span> {
		<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;api&#x27;</span>, <span class="hljs-string">&#x27;search&#x27;</span>),
			search = Search.parse(
				<span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.request({
					host,
					<span class="hljs-attr">path</span>: apiPath(query, page, sort),
				})
			);

		<span class="hljs-built_in">Object</span>.assign(search, {
			<span class="hljs-attr">api</span>: <span class="hljs-built_in">this</span>,
			query,
			page,
			sort,
		});

		<span class="hljs-keyword">return</span> search;
	}

	<span class="hljs-comment">/**
	 * Search by query.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span>          </span>query     Query.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[page=1]  Starting page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?SearchSortMode}</span> </span>[sort=&#x27;&#x27;] Search sort mode.
	 * <span class="hljs-doctag">@yields <span class="hljs-type">{Search}</span> </span>Search instance.
	 * <span class="hljs-doctag">@async
</span>
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{AsyncGenerator&lt;Search, Search, Search&gt;}</span>
</span>
	 */</span>
	<span class="hljs-keyword">async</span> * <span class="hljs-function"><span class="hljs-title">searchGenerator</span>(<span class="hljs-params">query, page = <span class="hljs-number">1</span>, sort = <span class="hljs-string">&#x27;&#x27;</span></span>)</span> {
		<span class="hljs-keyword">let</span> search = <span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.search(query, page, sort);

		<span class="hljs-keyword">while</span> (search.page &lt;= search.pages) {
			<span class="hljs-keyword">yield</span> search;
			search = <span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.search(query, search.page + <span class="hljs-number">1</span>, sort);
		}
	}

	<span class="hljs-comment">/**
	 * Search related books.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number|Book}</span> </span>book Book instance or Book ID.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Search&gt;}</span> </span>Search instance.
	 * <span class="hljs-doctag">@async
</span>
	 */</span>
	<span class="hljs-keyword">async</span> <span class="hljs-function"><span class="hljs-title">searchAlike</span>(<span class="hljs-params">book</span>)</span> {
		<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;api&#x27;</span>, <span class="hljs-string">&#x27;searchAlike&#x27;</span>);

		<span class="hljs-keyword">return</span> Search.parse(
			<span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.request({
				host,
				<span class="hljs-attr">path</span>: apiPath(
					book <span class="hljs-keyword">instanceof</span> Book
						? book.id
						: +book
				),
			})
		);
	}

	<span class="hljs-comment">/**
	 * Search by tag id.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number|Tag}</span>      </span>tag       Tag or Tag ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[page=1]  Page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?SearchSortMode}</span> </span>[sort=&#x27;&#x27;] Search sort mode.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Search&gt;}</span> </span>Search instance.
	 * <span class="hljs-doctag">@async
</span>
	 */</span>
	<span class="hljs-keyword">async</span> <span class="hljs-function"><span class="hljs-title">searchTagged</span>(<span class="hljs-params">tag, page = <span class="hljs-number">1</span>, sort = <span class="hljs-string">&#x27;&#x27;</span></span>)</span> {
		<span class="hljs-keyword">if</span> (!(tag <span class="hljs-keyword">instanceof</span> Tag))
			tag = Tag.get({ <span class="hljs-attr">id</span>: +tag, });
		<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;api&#x27;</span>, <span class="hljs-string">&#x27;searchTagged&#x27;</span>),
			search = Search.parse(
				<span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.request({
					host,
					<span class="hljs-attr">path</span>: apiPath(tag.id, page, sort),
				})
			);

		<span class="hljs-built_in">Object</span>.assign(search, {
			<span class="hljs-attr">api</span>  : <span class="hljs-built_in">this</span>,
			<span class="hljs-attr">query</span>: tag,
			page,
			sort,
		});

		<span class="hljs-keyword">return</span> search;
	}

	<span class="hljs-comment">/**
	 * Get book by id.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span> </span>bookID Book ID.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Book&gt;}</span> </span>Book instance.
	 * <span class="hljs-doctag">@async
</span>
	 */</span>
	<span class="hljs-keyword">async</span> <span class="hljs-function"><span class="hljs-title">getBook</span>(<span class="hljs-params">bookID</span>)</span> {
		<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;api&#x27;</span>, <span class="hljs-string">&#x27;book&#x27;</span>);

		<span class="hljs-keyword">return</span> Book.parse(
			<span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.request({
				host,
				<span class="hljs-attr">path</span>: apiPath(bookID),
			})
		);
	}

	<span class="hljs-comment">/**
	 * Get random book.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Book&gt;}</span> </span>Book instance.
	 * <span class="hljs-doctag">@async
</span>
	 */</span>
	<span class="hljs-keyword">async</span> <span class="hljs-function"><span class="hljs-title">getRandomBook</span>(<span class="hljs-params"></span>)</span> {
		<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;api&#x27;</span>, <span class="hljs-string">&#x27;randomBookRedirect&#x27;</span>);

		<span class="hljs-keyword">try</span> {
			<span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.request({
				host,
				<span class="hljs-attr">path</span>: apiPath(),
			}); <span class="hljs-comment">// Will always throw</span>
		} <span class="hljs-keyword">catch</span> (error) {
			<span class="hljs-keyword">if</span> (!(error <span class="hljs-keyword">instanceof</span> APIError))
				<span class="hljs-keyword">throw</span> error;
			<span class="hljs-keyword">const</span> response = error.httpResponse;
			<span class="hljs-keyword">if</span> (!response || response.statusCode !== <span class="hljs-number">302</span>)
				<span class="hljs-keyword">throw</span> error;
			<span class="hljs-keyword">const</span> id = +((<span class="hljs-regexp">/\d+/</span>).exec(response.headers.location) || {})[<span class="hljs-number">0</span>];
			<span class="hljs-keyword">if</span> (<span class="hljs-built_in">isNaN</span>(id))
				<span class="hljs-keyword">throw</span> APIError.absorb(<span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;Bad redirect&#x27;</span>), response);
			<span class="hljs-keyword">return</span> <span class="hljs-keyword">await</span> <span class="hljs-built_in">this</span>.getBook(id);
		}
	}

	<span class="hljs-comment">/**
	 * Get image URL.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Image}</span> </span>image Image.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>Image URL.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">getImageURL</span>(<span class="hljs-params">image</span>)</span> {
		<span class="hljs-keyword">if</span> (image <span class="hljs-keyword">instanceof</span> Image) {
			<span class="hljs-keyword">let</span> { host, apiPath, } = image.isCover
					? <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;thumbs&#x27;</span>, <span class="hljs-string">&#x27;bookCover&#x27;</span>)
					: <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;images&#x27;</span>, <span class="hljs-string">&#x27;bookPage&#x27;</span>),
				<span class="hljs-comment">// Handle the case where nhentai serves WebP files with original extension names</span>
				<span class="hljs-comment">// E.g., cover.jpg.webp instead of cover.jpg</span>
				extension = image.type.extension;

			<span class="hljs-comment">// For covers, if the original extension is not webp, try webp format</span>
			<span class="hljs-comment">// This handles cases where API returns &#x27;j&#x27; but file is actually cover.jpg.webp</span>
			<span class="hljs-keyword">if</span> (image.isCover &amp;&amp; extension !== <span class="hljs-string">&#x27;webp&#x27;</span>) {
				<span class="hljs-comment">// Try WebP format first for better compatibility with modern nhentai</span>
				<span class="hljs-keyword">const</span> webpUrl = <span class="hljs-string">`http<span class="hljs-subst">${<span class="hljs-built_in">this</span>.ssl ? <span class="hljs-string">&#x27;s&#x27;</span> : <span class="hljs-string">&#x27;&#x27;</span>}</span>://<span class="hljs-subst">${host}</span>`</span> +
					apiPath(image.book.media, <span class="hljs-string">`<span class="hljs-subst">${extension}</span>.webp`</span>);

				<span class="hljs-comment">// Return WebP URL - the consumer can handle fallback if needed</span>
				<span class="hljs-keyword">return</span> webpUrl;
			}

			<span class="hljs-keyword">return</span> <span class="hljs-string">`http<span class="hljs-subst">${<span class="hljs-built_in">this</span>.ssl ? <span class="hljs-string">&#x27;s&#x27;</span> : <span class="hljs-string">&#x27;&#x27;</span>}</span>://<span class="hljs-subst">${host}</span>`</span> + (image.isCover
				? apiPath(image.book.media, extension)
				: apiPath(image.book.media, image.id, extension));
		}
		<span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;image must be Image instance.&#x27;</span>);
	}

	<span class="hljs-comment">/**
	 * Get image URL with original extension (fallback for when WebP fails).
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Image}</span> </span>image Image.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>Image URL with original extension.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">getImageURLOriginal</span>(<span class="hljs-params">image</span>)</span> {
		<span class="hljs-keyword">if</span> (image <span class="hljs-keyword">instanceof</span> Image) {
			<span class="hljs-keyword">let</span> { host, apiPath, } = image.isCover
				? <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;thumbs&#x27;</span>, <span class="hljs-string">&#x27;bookCover&#x27;</span>)
				: <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;images&#x27;</span>, <span class="hljs-string">&#x27;bookPage&#x27;</span>);

			<span class="hljs-keyword">return</span> <span class="hljs-string">`http<span class="hljs-subst">${<span class="hljs-built_in">this</span>.ssl ? <span class="hljs-string">&#x27;s&#x27;</span> : <span class="hljs-string">&#x27;&#x27;</span>}</span>://<span class="hljs-subst">${host}</span>`</span> + (image.isCover
				? apiPath(image.book.media, image.type.extension)
				: apiPath(image.book.media, image.id, image.type.extension));
		}
		<span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;image must be Image instance.&#x27;</span>);
	}

	<span class="hljs-comment">/**
	 * Get image thumbnail URL.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Image}</span> </span>image Image.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{string}</span> </span>Image thumbnail URL.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">getThumbURL</span>(<span class="hljs-params">image</span>)</span> {
		<span class="hljs-keyword">if</span> (image <span class="hljs-keyword">instanceof</span> Image &amp;&amp; !image.isCover) {
			<span class="hljs-keyword">let</span> { host, apiPath, } = <span class="hljs-built_in">this</span>.getAPIArgs(<span class="hljs-string">&#x27;thumbs&#x27;</span>, <span class="hljs-string">&#x27;bookThumb&#x27;</span>);

			<span class="hljs-keyword">return</span> <span class="hljs-string">`http<span class="hljs-subst">${<span class="hljs-built_in">this</span>.ssl ? <span class="hljs-string">&#x27;s&#x27;</span> : <span class="hljs-string">&#x27;&#x27;</span>}</span>://<span class="hljs-subst">${host}</span>`</span>
				+ apiPath(image.book.media, image.id, image.type.extension);
		}
		<span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;image must be Image instance and not book cover.&#x27;</span>);
	}
}

<span class="hljs-keyword">export</span> <span class="hljs-keyword">default</span> API;
</code></pre>
    </div>
  </div>
</main>
            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
