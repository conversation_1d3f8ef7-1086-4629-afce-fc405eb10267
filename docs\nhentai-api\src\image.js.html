
<!-- Generated by webdoc on 28/05/2025, 10.03.48 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: image.js</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"sources"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs sources">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            

<main class="page-content">
  <div class="source">
    <div class="source__split">
      <ul>
        
          <li id="1">
            <a href="#1">1</a>
          </li>
        
          <li id="2">
            <a href="#2">2</a>
          </li>
        
          <li id="3">
            <a href="#3">3</a>
          </li>
        
          <li id="4">
            <a href="#4">4</a>
          </li>
        
          <li id="5">
            <a href="#5">5</a>
          </li>
        
          <li id="6">
            <a href="#6">6</a>
          </li>
        
          <li id="7">
            <a href="#7">7</a>
          </li>
        
          <li id="8">
            <a href="#8">8</a>
          </li>
        
          <li id="9">
            <a href="#9">9</a>
          </li>
        
          <li id="10">
            <a href="#10">10</a>
          </li>
        
          <li id="11">
            <a href="#11">11</a>
          </li>
        
          <li id="12">
            <a href="#12">12</a>
          </li>
        
          <li id="13">
            <a href="#13">13</a>
          </li>
        
          <li id="14">
            <a href="#14">14</a>
          </li>
        
          <li id="15">
            <a href="#15">15</a>
          </li>
        
          <li id="16">
            <a href="#16">16</a>
          </li>
        
          <li id="17">
            <a href="#17">17</a>
          </li>
        
          <li id="18">
            <a href="#18">18</a>
          </li>
        
          <li id="19">
            <a href="#19">19</a>
          </li>
        
          <li id="20">
            <a href="#20">20</a>
          </li>
        
          <li id="21">
            <a href="#21">21</a>
          </li>
        
          <li id="22">
            <a href="#22">22</a>
          </li>
        
          <li id="23">
            <a href="#23">23</a>
          </li>
        
          <li id="24">
            <a href="#24">24</a>
          </li>
        
          <li id="25">
            <a href="#25">25</a>
          </li>
        
          <li id="26">
            <a href="#26">26</a>
          </li>
        
          <li id="27">
            <a href="#27">27</a>
          </li>
        
          <li id="28">
            <a href="#28">28</a>
          </li>
        
          <li id="29">
            <a href="#29">29</a>
          </li>
        
          <li id="30">
            <a href="#30">30</a>
          </li>
        
          <li id="31">
            <a href="#31">31</a>
          </li>
        
          <li id="32">
            <a href="#32">32</a>
          </li>
        
          <li id="33">
            <a href="#33">33</a>
          </li>
        
          <li id="34">
            <a href="#34">34</a>
          </li>
        
          <li id="35">
            <a href="#35">35</a>
          </li>
        
          <li id="36">
            <a href="#36">36</a>
          </li>
        
          <li id="37">
            <a href="#37">37</a>
          </li>
        
          <li id="38">
            <a href="#38">38</a>
          </li>
        
          <li id="39">
            <a href="#39">39</a>
          </li>
        
          <li id="40">
            <a href="#40">40</a>
          </li>
        
          <li id="41">
            <a href="#41">41</a>
          </li>
        
          <li id="42">
            <a href="#42">42</a>
          </li>
        
          <li id="43">
            <a href="#43">43</a>
          </li>
        
          <li id="44">
            <a href="#44">44</a>
          </li>
        
          <li id="45">
            <a href="#45">45</a>
          </li>
        
          <li id="46">
            <a href="#46">46</a>
          </li>
        
          <li id="47">
            <a href="#47">47</a>
          </li>
        
          <li id="48">
            <a href="#48">48</a>
          </li>
        
          <li id="49">
            <a href="#49">49</a>
          </li>
        
          <li id="50">
            <a href="#50">50</a>
          </li>
        
          <li id="51">
            <a href="#51">51</a>
          </li>
        
          <li id="52">
            <a href="#52">52</a>
          </li>
        
          <li id="53">
            <a href="#53">53</a>
          </li>
        
          <li id="54">
            <a href="#54">54</a>
          </li>
        
          <li id="55">
            <a href="#55">55</a>
          </li>
        
          <li id="56">
            <a href="#56">56</a>
          </li>
        
          <li id="57">
            <a href="#57">57</a>
          </li>
        
          <li id="58">
            <a href="#58">58</a>
          </li>
        
          <li id="59">
            <a href="#59">59</a>
          </li>
        
          <li id="60">
            <a href="#60">60</a>
          </li>
        
          <li id="61">
            <a href="#61">61</a>
          </li>
        
          <li id="62">
            <a href="#62">62</a>
          </li>
        
          <li id="63">
            <a href="#63">63</a>
          </li>
        
          <li id="64">
            <a href="#64">64</a>
          </li>
        
          <li id="65">
            <a href="#65">65</a>
          </li>
        
          <li id="66">
            <a href="#66">66</a>
          </li>
        
          <li id="67">
            <a href="#67">67</a>
          </li>
        
          <li id="68">
            <a href="#68">68</a>
          </li>
        
          <li id="69">
            <a href="#69">69</a>
          </li>
        
          <li id="70">
            <a href="#70">70</a>
          </li>
        
          <li id="71">
            <a href="#71">71</a>
          </li>
        
          <li id="72">
            <a href="#72">72</a>
          </li>
        
          <li id="73">
            <a href="#73">73</a>
          </li>
        
          <li id="74">
            <a href="#74">74</a>
          </li>
        
          <li id="75">
            <a href="#75">75</a>
          </li>
        
          <li id="76">
            <a href="#76">76</a>
          </li>
        
          <li id="77">
            <a href="#77">77</a>
          </li>
        
          <li id="78">
            <a href="#78">78</a>
          </li>
        
          <li id="79">
            <a href="#79">79</a>
          </li>
        
          <li id="80">
            <a href="#80">80</a>
          </li>
        
          <li id="81">
            <a href="#81">81</a>
          </li>
        
          <li id="82">
            <a href="#82">82</a>
          </li>
        
          <li id="83">
            <a href="#83">83</a>
          </li>
        
          <li id="84">
            <a href="#84">84</a>
          </li>
        
          <li id="85">
            <a href="#85">85</a>
          </li>
        
          <li id="86">
            <a href="#86">86</a>
          </li>
        
          <li id="87">
            <a href="#87">87</a>
          </li>
        
          <li id="88">
            <a href="#88">88</a>
          </li>
        
          <li id="89">
            <a href="#89">89</a>
          </li>
        
          <li id="90">
            <a href="#90">90</a>
          </li>
        
          <li id="91">
            <a href="#91">91</a>
          </li>
        
          <li id="92">
            <a href="#92">92</a>
          </li>
        
          <li id="93">
            <a href="#93">93</a>
          </li>
        
          <li id="94">
            <a href="#94">94</a>
          </li>
        
          <li id="95">
            <a href="#95">95</a>
          </li>
        
          <li id="96">
            <a href="#96">96</a>
          </li>
        
          <li id="97">
            <a href="#97">97</a>
          </li>
        
          <li id="98">
            <a href="#98">98</a>
          </li>
        
          <li id="99">
            <a href="#99">99</a>
          </li>
        
          <li id="100">
            <a href="#100">100</a>
          </li>
        
          <li id="101">
            <a href="#101">101</a>
          </li>
        
          <li id="102">
            <a href="#102">102</a>
          </li>
        
          <li id="103">
            <a href="#103">103</a>
          </li>
        
          <li id="104">
            <a href="#104">104</a>
          </li>
        
          <li id="105">
            <a href="#105">105</a>
          </li>
        
          <li id="106">
            <a href="#106">106</a>
          </li>
        
          <li id="107">
            <a href="#107">107</a>
          </li>
        
          <li id="108">
            <a href="#108">108</a>
          </li>
        
          <li id="109">
            <a href="#109">109</a>
          </li>
        
          <li id="110">
            <a href="#110">110</a>
          </li>
        
          <li id="111">
            <a href="#111">111</a>
          </li>
        
          <li id="112">
            <a href="#112">112</a>
          </li>
        
          <li id="113">
            <a href="#113">113</a>
          </li>
        
          <li id="114">
            <a href="#114">114</a>
          </li>
        
          <li id="115">
            <a href="#115">115</a>
          </li>
        
          <li id="116">
            <a href="#116">116</a>
          </li>
        
          <li id="117">
            <a href="#117">117</a>
          </li>
        
          <li id="118">
            <a href="#118">118</a>
          </li>
        
          <li id="119">
            <a href="#119">119</a>
          </li>
        
          <li id="120">
            <a href="#120">120</a>
          </li>
        
          <li id="121">
            <a href="#121">121</a>
          </li>
        
          <li id="122">
            <a href="#122">122</a>
          </li>
        
          <li id="123">
            <a href="#123">123</a>
          </li>
        
          <li id="124">
            <a href="#124">124</a>
          </li>
        
          <li id="125">
            <a href="#125">125</a>
          </li>
        
          <li id="126">
            <a href="#126">126</a>
          </li>
        
          <li id="127">
            <a href="#127">127</a>
          </li>
        
          <li id="128">
            <a href="#128">128</a>
          </li>
        
          <li id="129">
            <a href="#129">129</a>
          </li>
        
          <li id="130">
            <a href="#130">130</a>
          </li>
        
          <li id="131">
            <a href="#131">131</a>
          </li>
        
          <li id="132">
            <a href="#132">132</a>
          </li>
        
          <li id="133">
            <a href="#133">133</a>
          </li>
        
          <li id="134">
            <a href="#134">134</a>
          </li>
        
          <li id="135">
            <a href="#135">135</a>
          </li>
        
          <li id="136">
            <a href="#136">136</a>
          </li>
        
          <li id="137">
            <a href="#137">137</a>
          </li>
        
          <li id="138">
            <a href="#138">138</a>
          </li>
        
          <li id="139">
            <a href="#139">139</a>
          </li>
        
          <li id="140">
            <a href="#140">140</a>
          </li>
        
          <li id="141">
            <a href="#141">141</a>
          </li>
        
          <li id="142">
            <a href="#142">142</a>
          </li>
        
          <li id="143">
            <a href="#143">143</a>
          </li>
        
          <li id="144">
            <a href="#144">144</a>
          </li>
        
          <li id="145">
            <a href="#145">145</a>
          </li>
        
          <li id="146">
            <a href="#146">146</a>
          </li>
        
          <li id="147">
            <a href="#147">147</a>
          </li>
        
          <li id="148">
            <a href="#148">148</a>
          </li>
        
          <li id="149">
            <a href="#149">149</a>
          </li>
        
          <li id="150">
            <a href="#150">150</a>
          </li>
        
          <li id="151">
            <a href="#151">151</a>
          </li>
        
          <li id="152">
            <a href="#152">152</a>
          </li>
        
          <li id="153">
            <a href="#153">153</a>
          </li>
        
          <li id="154">
            <a href="#154">154</a>
          </li>
        
          <li id="155">
            <a href="#155">155</a>
          </li>
        
          <li id="156">
            <a href="#156">156</a>
          </li>
        
          <li id="157">
            <a href="#157">157</a>
          </li>
        
          <li id="158">
            <a href="#158">158</a>
          </li>
        
          <li id="159">
            <a href="#159">159</a>
          </li>
        
          <li id="160">
            <a href="#160">160</a>
          </li>
        
          <li id="161">
            <a href="#161">161</a>
          </li>
        
          <li id="162">
            <a href="#162">162</a>
          </li>
        
          <li id="163">
            <a href="#163">163</a>
          </li>
        
          <li id="164">
            <a href="#164">164</a>
          </li>
        
          <li id="165">
            <a href="#165">165</a>
          </li>
        
          <li id="166">
            <a href="#166">166</a>
          </li>
        
          <li id="167">
            <a href="#167">167</a>
          </li>
        
          <li id="168">
            <a href="#168">168</a>
          </li>
        
          <li id="169">
            <a href="#169">169</a>
          </li>
        
          <li id="170">
            <a href="#170">170</a>
          </li>
        
          <li id="171">
            <a href="#171">171</a>
          </li>
        
          <li id="172">
            <a href="#172">172</a>
          </li>
        
          <li id="173">
            <a href="#173">173</a>
          </li>
        
          <li id="174">
            <a href="#174">174</a>
          </li>
        
          <li id="175">
            <a href="#175">175</a>
          </li>
        
          <li id="176">
            <a href="#176">176</a>
          </li>
        
          <li id="177">
            <a href="#177">177</a>
          </li>
        
          <li id="178">
            <a href="#178">178</a>
          </li>
        
          <li id="179">
            <a href="#179">179</a>
          </li>
        
          <li id="180">
            <a href="#180">180</a>
          </li>
        
          <li id="181">
            <a href="#181">181</a>
          </li>
        
          <li id="182">
            <a href="#182">182</a>
          </li>
        
          <li id="183">
            <a href="#183">183</a>
          </li>
        
          <li id="184">
            <a href="#184">184</a>
          </li>
        
          <li id="185">
            <a href="#185">185</a>
          </li>
        
          <li id="186">
            <a href="#186">186</a>
          </li>
        
          <li id="187">
            <a href="#187">187</a>
          </li>
        
          <li id="188">
            <a href="#188">188</a>
          </li>
        
          <li id="189">
            <a href="#189">189</a>
          </li>
        
          <li id="190">
            <a href="#190">190</a>
          </li>
        
          <li id="191">
            <a href="#191">191</a>
          </li>
        
          <li id="192">
            <a href="#192">192</a>
          </li>
        
          <li id="193">
            <a href="#193">193</a>
          </li>
        
          <li id="194">
            <a href="#194">194</a>
          </li>
        
          <li id="195">
            <a href="#195">195</a>
          </li>
        
          <li id="196">
            <a href="#196">196</a>
          </li>
        
          <li id="197">
            <a href="#197">197</a>
          </li>
        
          <li id="198">
            <a href="#198">198</a>
          </li>
        
          <li id="199">
            <a href="#199">199</a>
          </li>
        
          <li id="200">
            <a href="#200">200</a>
          </li>
        
          <li id="201">
            <a href="#201">201</a>
          </li>
        
          <li id="202">
            <a href="#202">202</a>
          </li>
        
          <li id="203">
            <a href="#203">203</a>
          </li>
        
          <li id="204">
            <a href="#204">204</a>
          </li>
        
          <li id="205">
            <a href="#205">205</a>
          </li>
        
          <li id="206">
            <a href="#206">206</a>
          </li>
        
          <li id="207">
            <a href="#207">207</a>
          </li>
        
          <li id="208">
            <a href="#208">208</a>
          </li>
        
          <li id="209">
            <a href="#209">209</a>
          </li>
        
          <li id="210">
            <a href="#210">210</a>
          </li>
        
          <li id="211">
            <a href="#211">211</a>
          </li>
        
          <li id="212">
            <a href="#212">212</a>
          </li>
        
          <li id="213">
            <a href="#213">213</a>
          </li>
        
          <li id="214">
            <a href="#214">214</a>
          </li>
        
          <li id="215">
            <a href="#215">215</a>
          </li>
        
          <li id="216">
            <a href="#216">216</a>
          </li>
        
          <li id="217">
            <a href="#217">217</a>
          </li>
        
          <li id="218">
            <a href="#218">218</a>
          </li>
        
          <li id="219">
            <a href="#219">219</a>
          </li>
        
          <li id="220">
            <a href="#220">220</a>
          </li>
        
          <li id="221">
            <a href="#221">221</a>
          </li>
        
          <li id="222">
            <a href="#222">222</a>
          </li>
        
          <li id="223">
            <a href="#223">223</a>
          </li>
        
          <li id="224">
            <a href="#224">224</a>
          </li>
        
          <li id="225">
            <a href="#225">225</a>
          </li>
        
          <li id="226">
            <a href="#226">226</a>
          </li>
        
          <li id="227">
            <a href="#227">227</a>
          </li>
        
          <li id="228">
            <a href="#228">228</a>
          </li>
        
          <li id="229">
            <a href="#229">229</a>
          </li>
        
          <li id="230">
            <a href="#230">230</a>
          </li>
        
          <li id="231">
            <a href="#231">231</a>
          </li>
        
          <li id="232">
            <a href="#232">232</a>
          </li>
        
          <li id="233">
            <a href="#233">233</a>
          </li>
        
          <li id="234">
            <a href="#234">234</a>
          </li>
        
          <li id="235">
            <a href="#235">235</a>
          </li>
        
          <li id="236">
            <a href="#236">236</a>
          </li>
        
          <li id="237">
            <a href="#237">237</a>
          </li>
        
          <li id="238">
            <a href="#238">238</a>
          </li>
        
          <li id="239">
            <a href="#239">239</a>
          </li>
        
          <li id="240">
            <a href="#240">240</a>
          </li>
        
          <li id="241">
            <a href="#241">241</a>
          </li>
        
          <li id="242">
            <a href="#242">242</a>
          </li>
        
          <li id="243">
            <a href="#243">243</a>
          </li>
        
          <li id="244">
            <a href="#244">244</a>
          </li>
        
          <li id="245">
            <a href="#245">245</a>
          </li>
        
          <li id="246">
            <a href="#246">246</a>
          </li>
        
          <li id="247">
            <a href="#247">247</a>
          </li>
        
          <li id="248">
            <a href="#248">248</a>
          </li>
        
          <li id="249">
            <a href="#249">249</a>
          </li>
        
          <li id="250">
            <a href="#250">250</a>
          </li>
        
          <li id="251">
            <a href="#251">251</a>
          </li>
        
          <li id="252">
            <a href="#252">252</a>
          </li>
        
          <li id="253">
            <a href="#253">253</a>
          </li>
        
          <li id="254">
            <a href="#254">254</a>
          </li>
        
          <li id="255">
            <a href="#255">255</a>
          </li>
        
          <li id="256">
            <a href="#256">256</a>
          </li>
        
          <li id="257">
            <a href="#257">257</a>
          </li>
        
          <li id="258">
            <a href="#258">258</a>
          </li>
        
          <li id="259">
            <a href="#259">259</a>
          </li>
        
          <li id="260">
            <a href="#260">260</a>
          </li>
        
          <li id="261">
            <a href="#261">261</a>
          </li>
        
      </ul>
      <pre><code><span class="hljs-comment">/**
 * <span class="hljs-doctag">@module <span class="hljs-variable">Image</span></span>
 */</span>

<span class="hljs-keyword">import</span> Book <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./book&#x27;</span>;

<span class="hljs-comment">/**
 * Image object from API.
 * <span class="hljs-doctag">@global
</span>
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">APIImage</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{string}</span>        </span>t Image type.
 * <span class="hljs-doctag">@property <span class="hljs-type">{number|string}</span> </span>w Image width.
 * <span class="hljs-doctag">@property <span class="hljs-type">{number|string}</span> </span>h Image height.
 */</span>

<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">ImageTypes</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{TagType}</span> </span>JPEG JPEG image type.
 * <span class="hljs-doctag">@property <span class="hljs-type">{TagType}</span> </span>PNG  PNG image type.
 * <span class="hljs-doctag">@property <span class="hljs-type">{TagType}</span> </span>GIF  GIF image type.
 */</span>

<span class="hljs-comment">/**
 * Class representing image type.
 * <span class="hljs-doctag">@class
</span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ImageType</span> </span>{
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{ImageTypes}</span>
</span>
	 * <span class="hljs-doctag">@static
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-type">{}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> knownTypes = {};

	<span class="hljs-comment">/**
	 * Image type name.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?string}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	<span class="hljs-keyword">type</span> = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * Image type extension.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?string}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	extension = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * Create image type.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>type      Image type name.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>extension Image type extension.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params"><span class="hljs-keyword">type</span>, extension</span>)</span> {
		<span class="hljs-keyword">if</span> (<span class="hljs-keyword">type</span>) {
			<span class="hljs-built_in">this</span>.type = <span class="hljs-keyword">type</span>;
			<span class="hljs-built_in">this</span>.constructor.knownTypes[<span class="hljs-keyword">type</span>] = <span class="hljs-built_in">this</span>;
		}
		<span class="hljs-built_in">this</span>.extension = extension;
	}

	<span class="hljs-comment">/**
	 * Whatever this tag type is unknown.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{boolean}</span>
</span>
	 */</span>
	<span class="hljs-keyword">get</span> <span class="hljs-title">isKnown</span>() {
		<span class="hljs-keyword">return</span> !(<span class="hljs-built_in">this</span> <span class="hljs-keyword">instanceof</span> UnknownImageType);
	}

	<span class="hljs-comment">/**
	 * Alias for type.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?string}</span>
</span>
	 */</span>
	<span class="hljs-keyword">get</span> <span class="hljs-title">name</span>() {
		<span class="hljs-keyword">return</span> <span class="hljs-built_in">this</span>.type;
	}
}

<span class="hljs-comment">/**
 * Class representing unknown image type.
 * <span class="hljs-doctag">@class
</span>
 * <span class="hljs-doctag">@extends <span class="hljs-variable">ImageType</span></span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">UnknownImageType</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">ImageType</span> </span>{
	<span class="hljs-comment">/**
	 * Create unknown image type.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>type      Unknown image type name.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>extension Unknown image type extension.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params"><span class="hljs-keyword">type</span>, extension</span>)</span> {
		<span class="hljs-built_in">super</span>(<span class="hljs-literal">null</span>, extension);
		<span class="hljs-built_in">this</span>.type = <span class="hljs-keyword">type</span>;
	}
}

<span class="hljs-comment">/**
 * Class representing image.
 * <span class="hljs-doctag">@class
</span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Image</span> </span>{
	<span class="hljs-comment">/**
	 * Image types.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{ImageTypes}</span>
</span>
	 * <span class="hljs-doctag">@static
</span>
	 */</span>
	<span class="hljs-keyword">static</span> types = {
		<span class="hljs-attr">JPEG</span>: <span class="hljs-keyword">new</span> ImageType(<span class="hljs-string">&#x27;jpeg&#x27;</span>, <span class="hljs-string">&#x27;jpg&#x27;</span>),
		<span class="hljs-attr">PNG</span> : <span class="hljs-keyword">new</span> ImageType(<span class="hljs-string">&#x27;png&#x27;</span>, <span class="hljs-string">&#x27;png&#x27;</span>),
		<span class="hljs-attr">GIF</span> : <span class="hljs-keyword">new</span> ImageType(<span class="hljs-string">&#x27;gif&#x27;</span>, <span class="hljs-string">&#x27;gif&#x27;</span>),
		<span class="hljs-attr">WEBP</span>: <span class="hljs-keyword">new</span> ImageType(<span class="hljs-string">&#x27;webp&#x27;</span>, <span class="hljs-string">&#x27;webp&#x27;</span>),

		<span class="hljs-attr">Unknown</span>: <span class="hljs-keyword">new</span> UnknownImageType(<span class="hljs-string">&#x27;unknown&#x27;</span>, <span class="hljs-string">&#x27;unknownExt&#x27;</span>),

		<span class="hljs-comment">/**
		 * Known image types.
		 * <span class="hljs-doctag">@type <span class="hljs-type">{ImageType}</span>
</span>
		 */</span>
		<span class="hljs-attr">known</span>: ImageType.knownTypes,

		<span class="hljs-comment">/**
		 * Get image type class instance by name.
		 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>type Image type.
		 * <span class="hljs-doctag">@returns <span class="hljs-type">{ImageType|UnknownImageType}</span> </span>Image type class instance.
		 */</span>
		<span class="hljs-function"><span class="hljs-title">get</span>(<span class="hljs-params"><span class="hljs-keyword">type</span></span>)</span> {
			<span class="hljs-keyword">let</span> known;
			<span class="hljs-keyword">if</span> (<span class="hljs-string">&#x27;string&#x27;</span> === <span class="hljs-keyword">typeof</span> <span class="hljs-keyword">type</span>) {
				<span class="hljs-keyword">type</span> = <span class="hljs-keyword">type</span>.toLowerCase();
				<span class="hljs-keyword">switch</span> (<span class="hljs-keyword">type</span>) {
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;j&#x27;</span>:
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;jpg&#x27;</span>:
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;jpeg&#x27;</span>:
						<span class="hljs-keyword">type</span> = <span class="hljs-string">&#x27;jpeg&#x27;</span>;
						<span class="hljs-keyword">break</span>;
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;p&#x27;</span>:
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;png&#x27;</span>:
						<span class="hljs-keyword">type</span> = <span class="hljs-string">&#x27;png&#x27;</span>;
						<span class="hljs-keyword">break</span>;
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;g&#x27;</span>:
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;gif&#x27;</span>:
						<span class="hljs-keyword">type</span> = <span class="hljs-string">&#x27;gif&#x27;</span>;
						<span class="hljs-keyword">break</span>;
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;w&#x27;</span>:
					<span class="hljs-keyword">case</span> <span class="hljs-string">&#x27;webp&#x27;</span>:
						<span class="hljs-keyword">type</span> = <span class="hljs-string">&#x27;webp&#x27;</span>;
						<span class="hljs-keyword">break</span>;
				}
			}
			<span class="hljs-keyword">return</span> ((known = <span class="hljs-built_in">this</span>.known[<span class="hljs-keyword">type</span>]))
				? known
				: <span class="hljs-keyword">new</span> UnknownImageType(<span class="hljs-keyword">type</span>);
		},
	};

	<span class="hljs-comment">/**
	 * Parse pure image object from API into class instance.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{APIImage}</span> </span>image  Image object
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span>   </span>[id=0] Image id (a.k.a. page number).
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Image}</span> </span>Image instance.
	 * <span class="hljs-doctag">@static
</span>
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">parse</span>(<span class="hljs-params">image, id = <span class="hljs-number">0</span></span>)</span> {
		<span class="hljs-keyword">let</span> {
			<span class="hljs-attr">t</span>: <span class="hljs-keyword">type</span>,
			<span class="hljs-attr">w</span>: width,
			<span class="hljs-attr">h</span>: height,
		} = image;

		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">this</span>({
			<span class="hljs-keyword">type</span>,
			<span class="hljs-attr">width</span> : +width,
			<span class="hljs-attr">height</span>: +height,
			id,
		});
	}

	<span class="hljs-comment">/**
	 * Image ID.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>0
	 */</span>
	id = <span class="hljs-number">0</span>;

	<span class="hljs-comment">/**
	 * Image width.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>0
	 */</span>
	width = <span class="hljs-number">0</span>;

	<span class="hljs-comment">/**
	 * Image height.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>0
	 */</span>
	height = <span class="hljs-number">0</span>;

	<span class="hljs-comment">/**
	 * Image type.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{ImageType}</span>
</span>
	 * <span class="hljs-doctag">@default </span>ImageTypes.JPEG
	 */</span>
	<span class="hljs-keyword">type</span> = <span class="hljs-built_in">this</span>.constructor.types.JPEG;

	<span class="hljs-comment">/**
	 * Image parent book.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{Book}</span>
</span>
	 * <span class="hljs-doctag">@default </span>Book.Unknown
	 */</span>
	book = Book.Unknown;

	<span class="hljs-comment">/**
	 * Create image.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{object}</span>           </span>[params]                      Image parameters.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span>           </span>[params.id=0]                 Image ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span>           </span>[params.width=0]              Image width.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{number}</span>           </span>[params.height=0]             Image height.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string|ImageType}</span> </span>[params.type=ImageTypes.JPEG] Image type.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Book}</span>             </span>[params.book=Book.Unknown]    Image&#x27;s Book.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params">{
		id     = <span class="hljs-number">0</span>,
		width  = <span class="hljs-number">0</span>,
		height = <span class="hljs-number">0</span>,
		<span class="hljs-keyword">type</span>   = <span class="hljs-built_in">this</span>.constructor.types.JPEG,
		book   = Book.Unknown,
	} = {}</span>)</span> {
		<span class="hljs-built_in">Object</span>.assign(<span class="hljs-built_in">this</span>, {
			<span class="hljs-attr">id</span>: <span class="hljs-string">&#x27;number&#x27;</span> === <span class="hljs-keyword">typeof</span> id
				? id &lt; <span class="hljs-number">1</span> ? <span class="hljs-number">0</span> : id
				: <span class="hljs-number">0</span>,
			width,
			height,
			<span class="hljs-attr">type</span>: <span class="hljs-keyword">type</span> <span class="hljs-keyword">instanceof</span> ImageType
				? <span class="hljs-keyword">type</span>
				: <span class="hljs-built_in">this</span>.constructor.types.get(<span class="hljs-keyword">type</span>),
			<span class="hljs-attr">book</span>: book <span class="hljs-keyword">instanceof</span> Book
				? book
				: Book.Unknown,
		});
	}

	<span class="hljs-comment">/**
	 * Whatever this image is book cover.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{boolean}</span>
</span>
	 */</span>
	<span class="hljs-keyword">get</span> <span class="hljs-title">isCover</span>() {
		<span class="hljs-keyword">return</span> <span class="hljs-built_in">this</span>.id &lt; <span class="hljs-number">1</span>;
	}

	<span class="hljs-comment">/**
	 * Image filename.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{string}</span>
</span>
	 */</span>
	<span class="hljs-keyword">get</span> <span class="hljs-title">filename</span>() {
		<span class="hljs-keyword">return</span> <span class="hljs-string">`<span class="hljs-subst">${<span class="hljs-built_in">this</span>.isCover ? <span class="hljs-string">&#x27;cover&#x27;</span> : <span class="hljs-built_in">this</span>.id}</span>.<span class="hljs-subst">${<span class="hljs-built_in">this</span>.<span class="hljs-keyword">type</span>.extension}</span>`</span>;
	}
}

<span class="hljs-keyword">export</span> <span class="hljs-keyword">default</span> Image;
</code></pre>
    </div>
  </div>
</main>
            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
