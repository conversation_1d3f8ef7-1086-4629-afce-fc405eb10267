
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: Tag</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>Tag</span>
    </div>
    <section class="document__title ">
      <h1>
        Tag
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class Tag</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Class representing tag.</p></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new Tag(params: object) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Create tag.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          params
        </td>
        <td class="member-parameter__type">
          object
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Tag parameters.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.id
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Tag id.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.type
        </td>
        <td class="member-parameter__type">
          string | <a href="%5Cnhentai-api%5Cnhentai-api%5CTagType.html">TagType</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          TagTypes.Unknown
        </td>
        <td class="member-parameter__description"><p>Tag type.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.name
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ""
        </td>
        <td class="member-parameter__description"><p>Tag name.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.count
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Tagged books count.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.url
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ""
        </td>
        <td class="member-parameter__description"><p>Tag URL.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from Tag</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTagTypes.html">TagTypes</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#types">static types</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#count">count = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#id">id = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#name">name = ""</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CTagType.html>TagType</a> | <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownTagType.html>UnknownTagType</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#type">type = TagTypes.Unknown</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#url">url = ""</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from Tag</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#get">static get(tag: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPITag.html">APITag</a> | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  boolean
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#compare">compare(tag: string | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, strict: boolean | string)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html#toString">toString(includeCount: ?boolean)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="types">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#types">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>types</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#96">
              tag.js:96
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static types: <a href=%5Cnhentai-api%5Cnhentai-api%5CTagTypes.html>TagTypes</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Tag types.</p></div>

  

  
  
</div>

  
    
<div class="member" id="count">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#count">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>count</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#171">
              tag.js:171
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">count: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Count of books tagged with this tag.</p></div>

  

  
  
</div>

  
    
<div class="member" id="id">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#id">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>id</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#150">
              tag.js:150
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">id: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Tag ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="name">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#name">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>name</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#164">
              tag.js:164
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">name: string = ""</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Tag name.</p></div>

  

  
  
</div>

  
    
<div class="member" id="type">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#type">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>type</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#157">
              tag.js:157
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">type: <a href=%5Cnhentai-api%5Cnhentai-api%5CTagType.html>TagType</a> | <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownTagType.html>UnknownTagType</a> = TagTypes.Unknown</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Tag type.</p></div>

  

  
  
</div>

  
    
<div class="member" id="url">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#url">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>url</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#178">
              tag.js:178
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">url: string = ""</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Tag URL.</p></div>

  

  
  
</div>

  
</div>

    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="get">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#get">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>get</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#132">
              tag.js:132
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static get(tag: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPITag.html">APITag</a> | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>) → {<a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Warp tag object with Tag class instance.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CAPITag.html">APITag</a> | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Tag to wrap.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        <td class="member-return__description"><p>Tag.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="compare">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#compare">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>compare</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#212">
              tag.js:212
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">compare(tag: string | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, strict: boolean | string) → {boolean}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Compare this to given one.
By default tags with different id will return false.
If you want to check whatever tag has any of properties from another tag pass <code>'any'</code> to <code>strict</code> parameter.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          string | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Tag to compare with.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          strict
        </td>
        <td class="member-parameter__type">
          boolean | string
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          false
        </td>
        <td class="member-parameter__description"><p>Whatever all parameters must be the same.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          boolean
        </td>
        <td class="member-return__description"><p>Whatever tags are equal.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="toString">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Tag.html#toString">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>toString</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\tag.js.html#242">
              tag.js:242
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">toString(includeCount: ?boolean) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get tag name or tag name with count of tagged books.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          includeCount
        </td>
        <td class="member-parameter__type">
          ?boolean
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          false
        </td>
        <td class="member-parameter__description"><p>Include count.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#types>types</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#count>count</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#id>id</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#name>name</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#type>type</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#url>url</a></li>
    
  </ul>
</div>

  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#get>get</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#compare>compare</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html#toString>toString</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
