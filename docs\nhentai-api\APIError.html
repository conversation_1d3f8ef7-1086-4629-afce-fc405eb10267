
<!-- Generated by webdoc on 28/05/2025, 10.03.48 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: APIError</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>APIError</span>
    </div>
    <section class="document__title ">
      <h1>
        APIError
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class APIError extends Error</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new APIError(message: string) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Error message.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          message
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        <td class="member-parameter__default">
          'Unknown error'
        </td>
        <td class="member-parameter__description"><p>Message.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from APIError</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  IncomingMessage
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#httpResponse">httpResponse = null</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  ?Error
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#originalError">originalError = null</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from APIError</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html">APIError</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#absorb">static absorb(error: Error, httpResponse: ?IncomingMessage)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  


  

  


  

  


  

</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="httpResponse">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIError.html#httpResponse">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>httpResponse</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\error.js.html#13">
              error.js:13
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">httpResponse: IncomingMessage = null</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>HTTP response.</p></div>

  

  
  
</div>

  
    
<div class="member" id="originalError">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIError.html#originalError">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>originalError</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\error.js.html#6">
              error.js:6
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">originalError: ?Error = null</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Original error.</p></div>

  

  
  
</div>

  
</div>

    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="absorb">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIError.html#absorb">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>absorb</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\error.js.html#28">
              error.js:28
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static absorb(error: Error, httpResponse: ?IncomingMessage) → {<a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html">APIError</a>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Absorb error.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          error
        </td>
        <td class="member-parameter__type">
          Error
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          null
        </td>
        <td class="member-parameter__description"><p>Original error.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          httpResponse
        </td>
        <td class="member-parameter__type">
          ?IncomingMessage
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          null
        </td>
        <td class="member-parameter__description"><p>HTTP response.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html">APIError</a>
        </td>
        <td class="member-return__description"></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#httpResponse>httpResponse</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#originalError>originalError</a></li>
    
  </ul>
</div>

  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIError.html#absorb>absorb</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
