environment:
  matrix:
    # - nodejs_version: "10"
    # - nodejs_version: "11"
    - nodejs_version: "12"
    - nodejs_version: "13"
    - nodejs_version: "14"
    - nodejs_version: "15"
    - nodejs_version: "16"

platform:
  - x64

cache:
  - "%LOCALAPPDATA%\\Yarn"

install:
  - ps: Install-Product node $env:nodejs_version
  - yarn install --frozen-lockfile --ignore-engines

build_script:
  - yarn build

before_test:
  - node --version
  - yarn --version

test_script:
  - yarn test

after_test:
  - yarn pack --filename artifact.tar.gz

artifacts:
  - path: artifact.tar.gz

deploy: off
