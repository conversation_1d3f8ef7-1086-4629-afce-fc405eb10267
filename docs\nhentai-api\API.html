
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: API</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>API</span>
    </div>
    <section class="document__title ">
      <h1>
        API
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class API</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Class used for interaction with nHentai API.</p></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new API(options: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CnHentaiOptions.html">nHentaiOptions</a>) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Applies provided options on top of defaults.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          options
        </td>
        <td class="member-parameter__type">
          ?<a href="%5Cnhentai-api%5Cnhentai-api%5CnHentaiOptions.html">nHentaiOptions</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          {}
        </td>
        <td class="member-parameter__description"><p>Options to apply.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from API</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#agent">agent</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  ?string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#cookies">cookies</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  ?<a href="%5Cnhentai-api%5Cnhentai-api%5CnHentaiHosts.html">nHentaiHosts</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#hosts">hosts</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  https | http
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#net">net</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  ?boolean
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#ssl">ssl</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from API</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getBook">getBook(bookID: number)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;string&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getCoverURLVariants">getCoverURLVariants(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getImageURL">getImageURL(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getImageURLOriginal">getImageURLOriginal(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getRandomBook">getRandomBook()</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getThumbURL">getThumbURL(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;object&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#request">request(options: object)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#search">search(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchAlike">searchAlike(book: number | <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  AsyncGenerator&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchGenerator">searchGenerator(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchTagged">searchTagged(tag: number | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="agent">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#agent">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>agent</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#153">
              api.js:153
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">agent</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>HTTP(S) agent.</p></div>

  

  
  
</div>

  
    
<div class="member" id="cookies">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#cookies">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>cookies</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#159">
              api.js:159
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">cookies: ?string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Cookies string.</p></div>

  

  
  
</div>

  
    
<div class="member" id="hosts">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#hosts">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>hosts</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#141">
              api.js:141
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">hosts: ?<a href=%5Cnhentai-api%5Cnhentai-api%5CnHentaiHosts.html>nHentaiHosts</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Hosts</p></div>

  

  
  
</div>

  
    
<div class="member" id="net">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#net">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>net</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#175">
              api.js:175
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">net: https | http</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get http(s) module depending on <code>options.ssl</code>.</p></div>

  

  
  
</div>

  
    
<div class="member" id="ssl">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#ssl">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>ssl</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#147">
              api.js:147
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">ssl: ?boolean</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Prefer HTTPS over HTTP.</p></div>

  

  
  
</div>

  
</div>

    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="getBook">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getBook">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getBook</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#389">
              api.js:389
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getBook(bookID: number) → {Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get book by id.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          bookID
        </td>
        <td class="member-parameter__type">
          number
        </td>
        
        
        <td class="member-parameter__description"><p>Book ID.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>&gt;
        </td>
        <td class="member-return__description"><p>Book instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getCoverURLVariants">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getCoverURLVariants">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getCoverURLVariants</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#528">
              api.js:528
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getCoverURLVariants(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>) → {Array&lt;string>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get all possible cover image URL variants for testing.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          image
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Cover image.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Array&lt;string&gt;
        </td>
        <td class="member-return__description"><p>Array of possible URLs to try.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getImageURL">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getImageURL">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getImageURL</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#482">
              api.js:482
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getImageURL(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get image URL.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          image
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Image.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>Image URL.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getImageURLOriginal">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getImageURLOriginal">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getImageURLOriginal</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#509">
              api.js:509
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getImageURLOriginal(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get image URL with original extension (fallback for when double extension fails).</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          image
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Image.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>Image URL with original extension.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getRandomBook">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getRandomBook">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getRandomBook</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#406">
              api.js:406
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getRandomBook() → {Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get random book.</p></div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>&gt;
        </td>
        <td class="member-return__description"><p>Book instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getThumbURL">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#getThumbURL">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getThumbURL</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#565">
              api.js:565
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getThumbURL(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>) → {string}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get image thumbnail URL.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          image
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Image.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          string
        </td>
        <td class="member-return__description"><p>Image thumbnail URL.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="request">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#request">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>request</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#202">
              api.js:202
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">request(options: object) → {Promise&lt;object>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>JSON get request.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          options
        </td>
        <td class="member-parameter__type">
          object
        </td>
        
        
        <td class="member-parameter__description"><p>HTTP(S) request options.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          options.host
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        
        <td class="member-parameter__description"><p>Host.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          options.path
        </td>
        <td class="member-parameter__type">
          string
        </td>
        
        
        <td class="member-parameter__description"><p>Path.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;object&gt;
        </td>
        <td class="member-return__description"><p>Parsed JSON.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="search">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#search">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>search</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#294">
              api.js:294
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">search(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>) → {Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search by query.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          query
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Query.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          ?number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          1
        </td>
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          sort
        </td>
        <td class="member-parameter__type">
          ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ''
        </td>
        <td class="member-parameter__description"><p>Search sort mode.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
        </td>
        <td class="member-return__description"><p>Search instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="searchAlike">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#searchAlike">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>searchAlike</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#339">
              api.js:339
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">searchAlike(book: number | <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>) → {Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search related books.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          book
        </td>
        <td class="member-parameter__type">
          number | <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Book instance or Book ID.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
        </td>
        <td class="member-return__description"><p>Search instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="searchGenerator">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#searchGenerator">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>searchGenerator</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#321">
              api.js:321
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">searchGenerator(query: string, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>) → {AsyncGenerator&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search by query.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          query
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Query.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          ?number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          1
        </td>
        <td class="member-parameter__description"><p>Starting page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          sort
        </td>
        <td class="member-parameter__type">
          ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ''
        </td>
        <td class="member-parameter__description"><p>Search sort mode.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          AsyncGenerator&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>, <a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
        </td>
        <td class="member-return__description"></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="searchTagged">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\API.html#searchTagged">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>searchTagged</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\api.js.html#360">
              api.js:360
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">searchTagged(tag: number | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, page: ?number, sort: ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>) → {Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Search by tag id.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          number | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Tag or Tag ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          page
        </td>
        <td class="member-parameter__type">
          ?number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          1
        </td>
        <td class="member-parameter__description"><p>Page ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          sort
        </td>
        <td class="member-parameter__type">
          ?<a href="%5Cnhentai-api%5Cnhentai-api%5CSearchSortMode.html">SearchSortMode</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ''
        </td>
        <td class="member-parameter__description"><p>Search sort mode.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          Promise&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a>&gt;
        </td>
        <td class="member-return__description"><p>Search instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#agent>agent</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#cookies>cookies</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#hosts>hosts</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#net>net</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#ssl>ssl</a></li>
    
  </ul>
</div>

  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getBook>getBook</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getCoverURLVariants>getCoverURLVariants</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getImageURL>getImageURL</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getImageURLOriginal>getImageURLOriginal</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getRandomBook>getRandomBook</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#getThumbURL>getThumbURL</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#request>request</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#search>search</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchAlike>searchAlike</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchGenerator>searchGenerator</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPI.html#searchTagged>searchTagged</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
