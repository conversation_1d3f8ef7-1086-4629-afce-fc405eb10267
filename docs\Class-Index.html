
<!-- Generated by webdoc on 28/05/2025, 10.03.48 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: Class Index</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs ">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<div class="page-content">
  <div class="document">
    <h1 class="document__title">Class Index</h1>
    <div class="api-index">
    
      <h2 class="api-index__title">A</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CAPI.html">API</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CAPIError.html">APIError</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CAPIPath.html">APIPath</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
      <h2 class="api-index__title">B</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
      <h2 class="api-index__title">I</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CImageType.html">ImageType</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
      <h2 class="api-index__title">S</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CSearch.html">Search</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
      <h2 class="api-index__title">T</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html">TagsArray</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CTagType.html">TagType</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
      <h2 class="api-index__title">U</h2>
      <table class="api-index-list">
        <tbody>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CUnknownBook.html">UnknownBook</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CUnknownImageType.html">UnknownImageType</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
            <tr class="api-index-list__row">
              <td class="api-index-list__row-title"><a href="%5Cnhentai-api%5Cnhentai-api%5CUnknownTagType.html">UnknownTagType</a></td>
              <td class="api-index-list__row-brief"></td>
            </tr>
          
        </tbody>
      </table>
    
    </div>
  </div>
</div>
<div>
  
</div>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
