
<!-- Generated by webdoc on 28/05/2025, 10.03.48 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: search.js</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"sources"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs sources">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            

<main class="page-content">
  <div class="source">
    <div class="source__split">
      <ul>
        
          <li id="1">
            <a href="#1">1</a>
          </li>
        
          <li id="2">
            <a href="#2">2</a>
          </li>
        
          <li id="3">
            <a href="#3">3</a>
          </li>
        
          <li id="4">
            <a href="#4">4</a>
          </li>
        
          <li id="5">
            <a href="#5">5</a>
          </li>
        
          <li id="6">
            <a href="#6">6</a>
          </li>
        
          <li id="7">
            <a href="#7">7</a>
          </li>
        
          <li id="8">
            <a href="#8">8</a>
          </li>
        
          <li id="9">
            <a href="#9">9</a>
          </li>
        
          <li id="10">
            <a href="#10">10</a>
          </li>
        
          <li id="11">
            <a href="#11">11</a>
          </li>
        
          <li id="12">
            <a href="#12">12</a>
          </li>
        
          <li id="13">
            <a href="#13">13</a>
          </li>
        
          <li id="14">
            <a href="#14">14</a>
          </li>
        
          <li id="15">
            <a href="#15">15</a>
          </li>
        
          <li id="16">
            <a href="#16">16</a>
          </li>
        
          <li id="17">
            <a href="#17">17</a>
          </li>
        
          <li id="18">
            <a href="#18">18</a>
          </li>
        
          <li id="19">
            <a href="#19">19</a>
          </li>
        
          <li id="20">
            <a href="#20">20</a>
          </li>
        
          <li id="21">
            <a href="#21">21</a>
          </li>
        
          <li id="22">
            <a href="#22">22</a>
          </li>
        
          <li id="23">
            <a href="#23">23</a>
          </li>
        
          <li id="24">
            <a href="#24">24</a>
          </li>
        
          <li id="25">
            <a href="#25">25</a>
          </li>
        
          <li id="26">
            <a href="#26">26</a>
          </li>
        
          <li id="27">
            <a href="#27">27</a>
          </li>
        
          <li id="28">
            <a href="#28">28</a>
          </li>
        
          <li id="29">
            <a href="#29">29</a>
          </li>
        
          <li id="30">
            <a href="#30">30</a>
          </li>
        
          <li id="31">
            <a href="#31">31</a>
          </li>
        
          <li id="32">
            <a href="#32">32</a>
          </li>
        
          <li id="33">
            <a href="#33">33</a>
          </li>
        
          <li id="34">
            <a href="#34">34</a>
          </li>
        
          <li id="35">
            <a href="#35">35</a>
          </li>
        
          <li id="36">
            <a href="#36">36</a>
          </li>
        
          <li id="37">
            <a href="#37">37</a>
          </li>
        
          <li id="38">
            <a href="#38">38</a>
          </li>
        
          <li id="39">
            <a href="#39">39</a>
          </li>
        
          <li id="40">
            <a href="#40">40</a>
          </li>
        
          <li id="41">
            <a href="#41">41</a>
          </li>
        
          <li id="42">
            <a href="#42">42</a>
          </li>
        
          <li id="43">
            <a href="#43">43</a>
          </li>
        
          <li id="44">
            <a href="#44">44</a>
          </li>
        
          <li id="45">
            <a href="#45">45</a>
          </li>
        
          <li id="46">
            <a href="#46">46</a>
          </li>
        
          <li id="47">
            <a href="#47">47</a>
          </li>
        
          <li id="48">
            <a href="#48">48</a>
          </li>
        
          <li id="49">
            <a href="#49">49</a>
          </li>
        
          <li id="50">
            <a href="#50">50</a>
          </li>
        
          <li id="51">
            <a href="#51">51</a>
          </li>
        
          <li id="52">
            <a href="#52">52</a>
          </li>
        
          <li id="53">
            <a href="#53">53</a>
          </li>
        
          <li id="54">
            <a href="#54">54</a>
          </li>
        
          <li id="55">
            <a href="#55">55</a>
          </li>
        
          <li id="56">
            <a href="#56">56</a>
          </li>
        
          <li id="57">
            <a href="#57">57</a>
          </li>
        
          <li id="58">
            <a href="#58">58</a>
          </li>
        
          <li id="59">
            <a href="#59">59</a>
          </li>
        
          <li id="60">
            <a href="#60">60</a>
          </li>
        
          <li id="61">
            <a href="#61">61</a>
          </li>
        
          <li id="62">
            <a href="#62">62</a>
          </li>
        
          <li id="63">
            <a href="#63">63</a>
          </li>
        
          <li id="64">
            <a href="#64">64</a>
          </li>
        
          <li id="65">
            <a href="#65">65</a>
          </li>
        
          <li id="66">
            <a href="#66">66</a>
          </li>
        
          <li id="67">
            <a href="#67">67</a>
          </li>
        
          <li id="68">
            <a href="#68">68</a>
          </li>
        
          <li id="69">
            <a href="#69">69</a>
          </li>
        
          <li id="70">
            <a href="#70">70</a>
          </li>
        
          <li id="71">
            <a href="#71">71</a>
          </li>
        
          <li id="72">
            <a href="#72">72</a>
          </li>
        
          <li id="73">
            <a href="#73">73</a>
          </li>
        
          <li id="74">
            <a href="#74">74</a>
          </li>
        
          <li id="75">
            <a href="#75">75</a>
          </li>
        
          <li id="76">
            <a href="#76">76</a>
          </li>
        
          <li id="77">
            <a href="#77">77</a>
          </li>
        
          <li id="78">
            <a href="#78">78</a>
          </li>
        
          <li id="79">
            <a href="#79">79</a>
          </li>
        
          <li id="80">
            <a href="#80">80</a>
          </li>
        
          <li id="81">
            <a href="#81">81</a>
          </li>
        
          <li id="82">
            <a href="#82">82</a>
          </li>
        
          <li id="83">
            <a href="#83">83</a>
          </li>
        
          <li id="84">
            <a href="#84">84</a>
          </li>
        
          <li id="85">
            <a href="#85">85</a>
          </li>
        
          <li id="86">
            <a href="#86">86</a>
          </li>
        
          <li id="87">
            <a href="#87">87</a>
          </li>
        
          <li id="88">
            <a href="#88">88</a>
          </li>
        
          <li id="89">
            <a href="#89">89</a>
          </li>
        
          <li id="90">
            <a href="#90">90</a>
          </li>
        
          <li id="91">
            <a href="#91">91</a>
          </li>
        
          <li id="92">
            <a href="#92">92</a>
          </li>
        
          <li id="93">
            <a href="#93">93</a>
          </li>
        
          <li id="94">
            <a href="#94">94</a>
          </li>
        
          <li id="95">
            <a href="#95">95</a>
          </li>
        
          <li id="96">
            <a href="#96">96</a>
          </li>
        
          <li id="97">
            <a href="#97">97</a>
          </li>
        
          <li id="98">
            <a href="#98">98</a>
          </li>
        
          <li id="99">
            <a href="#99">99</a>
          </li>
        
          <li id="100">
            <a href="#100">100</a>
          </li>
        
          <li id="101">
            <a href="#101">101</a>
          </li>
        
          <li id="102">
            <a href="#102">102</a>
          </li>
        
          <li id="103">
            <a href="#103">103</a>
          </li>
        
          <li id="104">
            <a href="#104">104</a>
          </li>
        
          <li id="105">
            <a href="#105">105</a>
          </li>
        
          <li id="106">
            <a href="#106">106</a>
          </li>
        
          <li id="107">
            <a href="#107">107</a>
          </li>
        
          <li id="108">
            <a href="#108">108</a>
          </li>
        
          <li id="109">
            <a href="#109">109</a>
          </li>
        
          <li id="110">
            <a href="#110">110</a>
          </li>
        
          <li id="111">
            <a href="#111">111</a>
          </li>
        
          <li id="112">
            <a href="#112">112</a>
          </li>
        
          <li id="113">
            <a href="#113">113</a>
          </li>
        
          <li id="114">
            <a href="#114">114</a>
          </li>
        
          <li id="115">
            <a href="#115">115</a>
          </li>
        
          <li id="116">
            <a href="#116">116</a>
          </li>
        
          <li id="117">
            <a href="#117">117</a>
          </li>
        
          <li id="118">
            <a href="#118">118</a>
          </li>
        
          <li id="119">
            <a href="#119">119</a>
          </li>
        
          <li id="120">
            <a href="#120">120</a>
          </li>
        
          <li id="121">
            <a href="#121">121</a>
          </li>
        
          <li id="122">
            <a href="#122">122</a>
          </li>
        
          <li id="123">
            <a href="#123">123</a>
          </li>
        
          <li id="124">
            <a href="#124">124</a>
          </li>
        
          <li id="125">
            <a href="#125">125</a>
          </li>
        
          <li id="126">
            <a href="#126">126</a>
          </li>
        
          <li id="127">
            <a href="#127">127</a>
          </li>
        
          <li id="128">
            <a href="#128">128</a>
          </li>
        
          <li id="129">
            <a href="#129">129</a>
          </li>
        
          <li id="130">
            <a href="#130">130</a>
          </li>
        
          <li id="131">
            <a href="#131">131</a>
          </li>
        
          <li id="132">
            <a href="#132">132</a>
          </li>
        
          <li id="133">
            <a href="#133">133</a>
          </li>
        
          <li id="134">
            <a href="#134">134</a>
          </li>
        
          <li id="135">
            <a href="#135">135</a>
          </li>
        
          <li id="136">
            <a href="#136">136</a>
          </li>
        
          <li id="137">
            <a href="#137">137</a>
          </li>
        
          <li id="138">
            <a href="#138">138</a>
          </li>
        
          <li id="139">
            <a href="#139">139</a>
          </li>
        
          <li id="140">
            <a href="#140">140</a>
          </li>
        
          <li id="141">
            <a href="#141">141</a>
          </li>
        
          <li id="142">
            <a href="#142">142</a>
          </li>
        
          <li id="143">
            <a href="#143">143</a>
          </li>
        
          <li id="144">
            <a href="#144">144</a>
          </li>
        
          <li id="145">
            <a href="#145">145</a>
          </li>
        
          <li id="146">
            <a href="#146">146</a>
          </li>
        
          <li id="147">
            <a href="#147">147</a>
          </li>
        
          <li id="148">
            <a href="#148">148</a>
          </li>
        
          <li id="149">
            <a href="#149">149</a>
          </li>
        
          <li id="150">
            <a href="#150">150</a>
          </li>
        
          <li id="151">
            <a href="#151">151</a>
          </li>
        
          <li id="152">
            <a href="#152">152</a>
          </li>
        
          <li id="153">
            <a href="#153">153</a>
          </li>
        
          <li id="154">
            <a href="#154">154</a>
          </li>
        
          <li id="155">
            <a href="#155">155</a>
          </li>
        
          <li id="156">
            <a href="#156">156</a>
          </li>
        
          <li id="157">
            <a href="#157">157</a>
          </li>
        
          <li id="158">
            <a href="#158">158</a>
          </li>
        
          <li id="159">
            <a href="#159">159</a>
          </li>
        
          <li id="160">
            <a href="#160">160</a>
          </li>
        
          <li id="161">
            <a href="#161">161</a>
          </li>
        
          <li id="162">
            <a href="#162">162</a>
          </li>
        
          <li id="163">
            <a href="#163">163</a>
          </li>
        
          <li id="164">
            <a href="#164">164</a>
          </li>
        
          <li id="165">
            <a href="#165">165</a>
          </li>
        
          <li id="166">
            <a href="#166">166</a>
          </li>
        
          <li id="167">
            <a href="#167">167</a>
          </li>
        
          <li id="168">
            <a href="#168">168</a>
          </li>
        
          <li id="169">
            <a href="#169">169</a>
          </li>
        
          <li id="170">
            <a href="#170">170</a>
          </li>
        
          <li id="171">
            <a href="#171">171</a>
          </li>
        
          <li id="172">
            <a href="#172">172</a>
          </li>
        
          <li id="173">
            <a href="#173">173</a>
          </li>
        
          <li id="174">
            <a href="#174">174</a>
          </li>
        
          <li id="175">
            <a href="#175">175</a>
          </li>
        
          <li id="176">
            <a href="#176">176</a>
          </li>
        
          <li id="177">
            <a href="#177">177</a>
          </li>
        
          <li id="178">
            <a href="#178">178</a>
          </li>
        
          <li id="179">
            <a href="#179">179</a>
          </li>
        
          <li id="180">
            <a href="#180">180</a>
          </li>
        
          <li id="181">
            <a href="#181">181</a>
          </li>
        
          <li id="182">
            <a href="#182">182</a>
          </li>
        
          <li id="183">
            <a href="#183">183</a>
          </li>
        
          <li id="184">
            <a href="#184">184</a>
          </li>
        
      </ul>
      <pre><code><span class="hljs-comment">/**
 * <span class="hljs-doctag">@module <span class="hljs-variable">Search</span></span>
 */</span>

<span class="hljs-keyword">import</span> API <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./api&#x27;</span>;
<span class="hljs-keyword">import</span> Book <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./book&#x27;</span>;
<span class="hljs-keyword">import</span> { Tag, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./tag&#x27;</span>;


<span class="hljs-comment">/**
 * Search object from API.
 * <span class="hljs-doctag">@global
</span>
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{object}</span> <span class="hljs-variable">APISearch</span></span>
 * <span class="hljs-doctag">@property <span class="hljs-type">{APIBook[]}</span>     </span>result    Search results.
 * <span class="hljs-doctag">@property <span class="hljs-type">{number|string}</span> </span>num_pages Number of search pages available.
 * <span class="hljs-doctag">@property <span class="hljs-type">{number|string}</span> </span>per_page  Number of books per page.
 */</span>


<span class="hljs-comment">/**
 * <span class="hljs-doctag">@typedef <span class="hljs-type">{&#x27;&#x27;|&#x27;popular&#x27;|&#x27;popular-week&#x27;|&#x27;popular-today&#x27;|&#x27;popular-month&#x27;}</span> <span class="hljs-variable">SearchSortMode</span></span>
 */</span>

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SearchSort</span> </span>{
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> Recent = <span class="hljs-string">&#x27;&#x27;</span>;
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> Popular = <span class="hljs-string">&#x27;popular&#x27;</span>;
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> PopularMonth = <span class="hljs-string">&#x27;popular-month&#x27;</span>;
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> PopularWeek = <span class="hljs-string">&#x27;popular-week&#x27;</span>;
	<span class="hljs-comment">/**
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> PopularToday = <span class="hljs-string">&#x27;poplar-today&#x27;</span>;
}

<span class="hljs-comment">/**
 * Class representing search request results.
 * <span class="hljs-doctag">@class
</span>
 */</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Search</span> </span>{
	<span class="hljs-comment">/**
	 * Parse search object into class instance.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{APISearch}</span> </span>search Search object.
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">parse</span>(<span class="hljs-params">search</span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">this</span>({
			<span class="hljs-attr">pages</span>: search.num_pages
				? +search.num_pages
				: <span class="hljs-number">1</span>,
			<span class="hljs-attr">perPage</span>: search.per_page
				? +search.per_page
				: search.result.length,
			<span class="hljs-attr">books</span>: search.result.map(Book.parse.bind(Book)),
		});
	}

	<span class="hljs-comment">/**
	 * API instance.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?API}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	api = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * Query string.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?string}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	query = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * Search sort mode.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{SearchSortMode}</span>
</span>
	 * <span class="hljs-doctag">@default </span>&#x27;&#x27;
	 */</span>
	sort = <span class="hljs-string">&#x27;&#x27;</span>;

	<span class="hljs-comment">/**
	 * Page ID.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>1
	 */</span>
	page = <span class="hljs-number">1</span>;

	<span class="hljs-comment">/**
	 * Books per page.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>0
	 */</span>
	perPage = <span class="hljs-number">0</span>;

	<span class="hljs-comment">/**
	 * Books array.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{Book[]}</span>
</span>
	 * <span class="hljs-doctag">@default </span>[]
	 */</span>
	books = [];

	<span class="hljs-comment">/**
	 * Pages count.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{number}</span>
</span>
	 * <span class="hljs-doctag">@default </span>1
	 */</span>
	pages = <span class="hljs-number">1</span>;

	<span class="hljs-comment">/**
	 * Create search.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?object}</span>         </span>[params]           Search parameters.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?string}</span>         </span>[params.query=&#x27;&#x27;]  Query string.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?SearchSortMode}</span> </span>[params.sort=&#x27;&#x27;]   Search sort mode.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[params.page=1]    Search page ID.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[params.pages=1]   Search pages count.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?number}</span>         </span>[params.perPage=0] Search books per page.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?Book[]}</span>         </span>[params.books=[]]  Books array.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params">{
		query   = <span class="hljs-literal">null</span>,
		sort    = <span class="hljs-string">&#x27;&#x27;</span>,
		page    = <span class="hljs-number">1</span>,
		pages   = <span class="hljs-number">1</span>,
		perPage = <span class="hljs-number">0</span>,
		books   = [],
	} = {}</span>)</span> {
		<span class="hljs-keyword">if</span> (<span class="hljs-built_in">Array</span>.isArray(books))
			books.forEach(<span class="hljs-built_in">this</span>.pushBook.bind(<span class="hljs-built_in">this</span>));

		<span class="hljs-built_in">Object</span>.assign(<span class="hljs-built_in">this</span>, {
			query,
			sort,
			page,
			pages,
			perPage,
		});
	}

	<span class="hljs-comment">/**
	 * Push book to books array.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Book}</span> </span>book Book.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{boolean}</span> </span>Whatever was book added or not.
	 * <span class="hljs-doctag">@private
</span>
	 */</span>
	<span class="hljs-function"><span class="hljs-title">pushBook</span>(<span class="hljs-params">book</span>)</span> {
		<span class="hljs-keyword">if</span> (book <span class="hljs-keyword">instanceof</span> Book) {
			<span class="hljs-built_in">this</span>.books.push(book);
			<span class="hljs-keyword">return</span> <span class="hljs-literal">true</span>;
		}
		<span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
	}

	<span class="hljs-comment">/**
	 * Request next page.
	 * <span class="hljs-doctag">@throws </span>Error if search request can&#x27;t be paginated.
	 * <span class="hljs-doctag">@throws </span>Error if `api` is missing as instance property or function argument.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{API}</span> </span>[api=this.api] API instance.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{Promise&lt;Search&gt;}</span> </span>Next page search.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">getNextPage</span>(<span class="hljs-params">api = <span class="hljs-built_in">this</span>.api</span>)</span> {
		<span class="hljs-keyword">let</span> { query, page, sort, } = <span class="hljs-built_in">this</span>;
		<span class="hljs-keyword">if</span> (query === <span class="hljs-literal">null</span>)
			<span class="hljs-keyword">throw</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;pagination impossible.&#x27;</span>);
		<span class="hljs-keyword">if</span> (!(api <span class="hljs-keyword">instanceof</span> API))
			<span class="hljs-keyword">throw</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">&#x27;api must exists.&#x27;</span>);
		<span class="hljs-keyword">return</span> query <span class="hljs-keyword">instanceof</span> Tag
			? api.searchTagged(query, page + <span class="hljs-number">1</span>, sort)
			: api.search(query, page + <span class="hljs-number">1</span>, sort);
	}
}

<span class="hljs-keyword">export</span> {
	Search,
	SearchSort,
};
</code></pre>
    </div>
  </div>
</main>
            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
