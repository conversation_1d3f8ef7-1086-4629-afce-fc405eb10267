
<!-- Generated by webdoc on 07/06/2025, 14.10.59 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: Documentation</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs ">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<div class="page-content">
  <div class="homepage">
  
    <div class="homepage__readme md">
      <h1>Node.JS client for nhentai.net undocumented APIs.</h1>
<p><a href="https://www.npmjs.com/package/nhentai-api"><img src="https://img.shields.io/npm/v/nhentai-api?style=for-the-badge" alt="npm version"></a><img src="https://img.shields.io/node/v/nhentai-api?style=for-the-badge" alt="node version"><a href="https://travis-ci.org/github/Zekfad/nhentai-api"><img src="https://img.shields.io/travis/Zekfad/nhentai-api?style=for-the-badge&amp;logo=linux&amp;logoColor=white" alt="Build status - Linux/OSX"></a><a href="https://ci.appveyor.com/project/Zekfad/nhentai-api"><img src="https://img.shields.io/appveyor/build/Zekfad/nhentai-api?style=for-the-badge&amp;logo=windows&amp;logoColor=white" alt="Build status - Windows"></a><a href="https://codecov.io/gh/Zekfad/nhentai-api"><img src="https://img.shields.io/codecov/c/gh/Zekfad/nhentai-api?style=for-the-badge" alt="Codecov"></a></p>
<p>Node.JS module for handling nhentai.net API.</p>
<h2>Features</h2>
<ul>
<li>Objective-oriented API abstraction.</li>
<li>Bidirectional inspecting (get image from book/get book from image).</li>
<li>Easy proxy support by using custom Agent (like <a href="https://www.npmjs.com/package/https-proxy-agent">this one</a>).</li>
<li>Return URLs for binary data (images).</li>
<li><strong>Multiple host support</strong> for load balancing (i1, i2, i3 for images; t1, t2, t3 for thumbnails).</li>
<li><strong>WebP image format support</strong> alongside JPEG, PNG, and GIF.</li>
<li><strong>Cookie support</strong> for authenticated requests.</li>
</ul>
<h2>Install</h2>
<p>Install via yarn:</p>
<pre><code class="hljs">yarn <span class="hljs-built_in">add</span> nhentai-api
</code></pre>
<p>Install via npm:</p>
<pre><code class="hljs"><span class="hljs-built_in">npm</span> i nhentai-api
</code></pre>
<h2>Docs</h2>
<p><a href="https://zekfad.github.io/nhentai-api/">Read the docs on GitHub pages.</a></p>
<h2>Example</h2>
<h3>Import</h3>
<h4>CommonJS</h4>
<pre><code class="hljs language-js"><span class="hljs-keyword">const</span> { <span class="hljs-variable constant_">API</span>, <span class="hljs-title class_">TagTypes</span>, } = <span class="hljs-built_in">require</span>(<span class="hljs-string">&#x27;nhentai-api&#x27;</span>);
</code></pre>
<h4>ES6</h4>
<pre><code class="hljs language-js"><span class="hljs-keyword">import</span> { <span class="hljs-variable constant_">API</span>, <span class="hljs-title class_">TagTypes</span>, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;nhentai-api&#x27;</span>;
</code></pre>
<h3>Use</h3>
<h4>Initialize API client</h4>
<pre><code class="hljs language-js"><span class="hljs-keyword">const</span> api = <span class="hljs-keyword">new</span> <span class="hljs-title function_">API</span>();
</code></pre>
<h4>Initialize API client with custom options</h4>
<pre><code class="hljs language-js"><span class="hljs-comment">// With custom hosts (multiple hosts for load balancing)</span>
<span class="hljs-keyword">const</span> api = <span class="hljs-keyword">new</span> <span class="hljs-title function_">API</span>({
	<span class="hljs-attr">hosts</span>: {
		<span class="hljs-attr">api</span>: <span class="hljs-string">&#x27;nhentai.net&#x27;</span>,
		<span class="hljs-attr">images</span>: [<span class="hljs-string">&#x27;i1.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;i2.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;i3.nhentai.net&#x27;</span>], <span class="hljs-comment">// Multiple image hosts</span>
		<span class="hljs-attr">thumbs</span>: [<span class="hljs-string">&#x27;t1.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;t2.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;t3.nhentai.net&#x27;</span>], <span class="hljs-comment">// Multiple thumbnail hosts</span>
	}
});

<span class="hljs-comment">// With cookies support</span>
<span class="hljs-keyword">const</span> api = <span class="hljs-keyword">new</span> <span class="hljs-title function_">API</span>({
	<span class="hljs-attr">cookies</span>: <span class="hljs-string">&#x27;sessionid=abc123;csrftoken=def456;other=value&#x27;</span>
});

<span class="hljs-comment">// With both custom hosts and cookies</span>
<span class="hljs-keyword">const</span> api = <span class="hljs-keyword">new</span> <span class="hljs-title function_">API</span>({
	<span class="hljs-attr">hosts</span>: {
		<span class="hljs-attr">images</span>: [<span class="hljs-string">&#x27;i1.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;i2.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;i3.nhentai.net&#x27;</span>],
		<span class="hljs-attr">thumbs</span>: [<span class="hljs-string">&#x27;t1.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;t2.nhentai.net&#x27;</span>, <span class="hljs-string">&#x27;t3.nhentai.net&#x27;</span>],
	},
	<span class="hljs-attr">cookies</span>: <span class="hljs-string">&#x27;sessionid=abc123;csrftoken=def456&#x27;</span>
});
</code></pre>
<h4>Get the book</h4>
<pre><code class="hljs language-js">api.<span class="hljs-title function_">getBook</span>(<span class="hljs-number">177013</span>).<span class="hljs-title function_">then</span>(<span class="hljs-function"><span class="hljs-params">book</span> =&gt;</span> {
	api.<span class="hljs-title function_">getImageURL</span>(book.<span class="hljs-property">cover</span>);    <span class="hljs-comment">// https://t.nhentai.net/galleries/987560/cover.jpg</span>
	api.<span class="hljs-title function_">getImageURL</span>(book.<span class="hljs-property">pages</span>[<span class="hljs-number">1</span>]); <span class="hljs-comment">// https://i.nhentai.net/galleries/987560/2.jpg</span>
});
</code></pre>
<h4>Get random book</h4>
<pre><code class="hljs language-js"><span class="hljs-keyword">await</span> api.<span class="hljs-title function_">getRandomBook</span>(); <span class="hljs-comment">// Book instance</span>
</code></pre>
<h4>Search for the books</h4>
<pre><code class="hljs language-js">api.<span class="hljs-title function_">search</span>(<span class="hljs-string">&#x27;test&#x27;</span>).<span class="hljs-title function_">then</span>(<span class="hljs-keyword">async</span> search =&gt; {
	<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(search); <span class="hljs-comment">/*
	Search {
		api: API { hosts: [Object], ssl: true, agent: [Agent] },
		query: &#x27;test&#x27;,
		page: 1,
		perPage: 25,
		books: [
			[Book], [Book], [Book], [Book],
			[Book], [Book], [Book], [Book],
			[Book], [Book], [Book], [Book],
			[Book], [Book], [Book], [Book],
			[Book], [Book], [Book], [Book],
			[Book], [Book], [Book], [Book],
			[Book]
		],
		pages: 67
	} */</span>
	search = <span class="hljs-keyword">await</span> search.<span class="hljs-title function_">getNextPage</span>(); <span class="hljs-comment">// Same as api.search(&#x27;text&#x27;, 2);</span>
	<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(search); <span class="hljs-comment">/*
	Search {
		// Same as above
		page: 2,
		// Same as above
		books: [
			// Books from 2nd page of search
		],
		// Same as above
	} */</span>
});
</code></pre>
<p>In case you need to change API implementation (e.g. change proxy) you can pass <code>api</code> to</p>
<pre><code class="hljs language-js">search.<span class="hljs-title function_">getNextPage</span>(api);
</code></pre>
<p>You can also use async generator:</p>
<pre><code class="hljs language-js"><span class="hljs-comment">// Get first 2 pages</span>
<span class="hljs-keyword">for</span> <span class="hljs-keyword">await</span> (<span class="hljs-keyword">const</span> search <span class="hljs-keyword">of</span> api.<span class="hljs-title function_">searchGenerator</span>(<span class="hljs-string">&#x27;test&#x27;</span>)) {
	<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(search);
	<span class="hljs-keyword">if</span> (search.<span class="hljs-property">page</span> &gt;= <span class="hljs-number">2</span>)
		<span class="hljs-keyword">break</span>;
}
</code></pre>
<h4>Working with tags</h4>
<p><code>book.tags</code> has type of <code>TagsArray</code>, it is just an <code>Array&lt;Tag&gt;</code> with helper methods.</p>
<pre><code class="hljs language-js"><span class="hljs-keyword">const</span> tag = book.<span class="hljs-property">tags</span>[<span class="hljs-number">0</span>]; <span class="hljs-comment">// Tag</span>
</code></pre>
<h5>Get tag id</h5>
<pre><code class="hljs language-js">tag.<span class="hljs-property">id</span>;
</code></pre>
<h5>Get tag name (without count)</h5>
<pre><code class="hljs language-js">tag.<span class="hljs-property">name</span>;
<span class="hljs-comment">// or</span>
tag.<span class="hljs-title function_">toString</span>();
</code></pre>
<h5>Get tag name (with count)</h5>
<pre><code class="hljs language-js">tag.<span class="hljs-title function_">toString</span>(<span class="hljs-literal">true</span>);
</code></pre>
<h5>Get tag type (as string)</h5>
<pre><code class="hljs language-js">tag.<span class="hljs-property">type</span>.<span class="hljs-property">type</span>;
<span class="hljs-comment">// or</span>
tag.<span class="hljs-property">type</span>.<span class="hljs-title function_">toString</span>();
</code></pre>
<h5>Pre-filtered tags</h5>
<pre><code class="hljs language-js">book.<span class="hljs-property">pureTags</span>;   <span class="hljs-comment">// pure tags (with type &#x27;tag&#x27;)</span>
book.<span class="hljs-property">categories</span>; <span class="hljs-comment">// category tags</span>
book.<span class="hljs-property">artists</span>;    <span class="hljs-comment">// artist tags</span>
book.<span class="hljs-property">parodies</span>;   <span class="hljs-comment">// parody tags</span>
book.<span class="hljs-property">characters</span>; <span class="hljs-comment">// character tags</span>
book.<span class="hljs-property">groups</span>;     <span class="hljs-comment">// group tags</span>
book.<span class="hljs-property">languages</span>;  <span class="hljs-comment">// language tags</span>
</code></pre>
<h5>Filter tags</h5>
<p>Get artists:</p>
<pre><code class="hljs language-js">book.<span class="hljs-property">artists</span>;
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({
	<span class="hljs-attr">type</span>: <span class="hljs-title class_">TagTypes</span>.<span class="hljs-property">Artist</span>, <span class="hljs-comment">// you may also use Tag.types.Artist or Tag.types.get(&#x27;artist&#x27;)</span>
}); <span class="hljs-comment">// TagsArray (subclass of Array&lt;Tag&gt;)</span>
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-string">&#x27;artist&#x27;</span>, }); <span class="hljs-comment">// TagsArray (subclass of Array&lt;Tag&gt;)</span>
</code></pre>
<p>Get tag with name <code>english</code>:</p>
<pre><code class="hljs language-js">book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">name</span>: <span class="hljs-string">&#x27;english&#x27;</span>, }); <span class="hljs-comment">// TagsArray (subclass of Array&lt;Tag&gt;)</span>
</code></pre>
<p>Get categories</p>
<pre><code class="hljs language-js">book.<span class="hljs-property">categories</span>;
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-title class_">TagTypes</span>.<span class="hljs-property">Category</span>, }); <span class="hljs-comment">// Recommended</span>
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-title class_">Tag</span>.<span class="hljs-property">types</span>.<span class="hljs-property">Category</span>, });
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-title class_">Tag</span>.<span class="hljs-property">types</span>.<span class="hljs-title function_">get</span>(<span class="hljs-string">&#x27;category&#x27;</span>), });
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-string">&#x27;category&#x27;</span>, });
</code></pre>
<h5>Get tags comma separated</h5>
<p>Without counts:</p>
<pre><code class="hljs language-js">book.<span class="hljs-property">tags</span>.<span class="hljs-title function_">toNames</span>().<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// or</span>
book.<span class="hljs-property">tags</span>.<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
</code></pre>
<p>With counts:</p>
<pre><code class="hljs language-js">book.<span class="hljs-property">tags</span>.<span class="hljs-title function_">toNames</span>(<span class="hljs-literal">true</span>).<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// or</span>
book.<span class="hljs-property">tags</span>.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">tag</span> =&gt;</span> tag.<span class="hljs-title function_">toString</span>(<span class="hljs-literal">true</span>)).<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>)
</code></pre>
<p>Get all artists:</p>
<pre><code class="hljs language-js"><span class="hljs-comment">// With counts</span>
book.<span class="hljs-property">artists</span>.<span class="hljs-title function_">toNames</span>(<span class="hljs-literal">true</span>).<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-title class_">TagTypes</span>.<span class="hljs-property">Artist</span>, }).<span class="hljs-title function_">toNames</span>(<span class="hljs-literal">true</span>).<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// Without counts</span>
book.<span class="hljs-property">artists</span>.<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// or</span>
book.<span class="hljs-property">artists</span>.<span class="hljs-title function_">toNames</span>().<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
<span class="hljs-comment">// or</span>
book.<span class="hljs-title function_">getTagsWith</span>({ <span class="hljs-attr">type</span>: <span class="hljs-title class_">TagTypes</span>.<span class="hljs-property">Artist</span>, }).<span class="hljs-title function_">join</span>(<span class="hljs-string">&#x27;, &#x27;</span>);
</code></pre>

    </div>
  
  </div>
</div>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
