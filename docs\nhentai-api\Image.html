
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: Image</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>Image</span>
    </div>
    <section class="document__title ">
      <h1>
        Image
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class Image</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Class representing image.</p></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new Image(params: object) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Create image.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          params
        </td>
        <td class="member-parameter__type">
          object
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Image parameters.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.id
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Image ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.width
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Image width.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.height
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Image height.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.type
        </td>
        <td class="member-parameter__type">
          string | <a href="%5Cnhentai-api%5Cnhentai-api%5CImageType.html">ImageType</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ImageTypes.JPEG
        </td>
        <td class="member-parameter__description"><p>Image type.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.book
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          Book.Unknown
        </td>
        <td class="member-parameter__description"><p>Image's Book.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from Image</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImageTypes.html">ImageTypes</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#types">static types</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html>Book</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#book">book = Book.Unknown</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#filename">filename</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#height">height = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#id">id = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  boolean
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#isCover">isCover</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CImageType.html>ImageType</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#type">type = ImageTypes.JPEG</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#width">width = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from Image</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html#parse">static parse(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html">APIImage</a>, id: number)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="types">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#types">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>types</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#101">
              image.js:101
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static types: <a href=%5Cnhentai-api%5Cnhentai-api%5CImageTypes.html>ImageTypes</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image types.</p></div>

  

  
  
</div>

  
    
<div class="member" id="book">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#book">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>book</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#205">
              image.js:205
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">book: <a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html>Book</a> = Book.Unknown</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image parent book.</p></div>

  

  
  
</div>

  
    
<div class="member" id="filename">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#filename">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>filename</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#251">
              image.js:251
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">filename: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image filename.</p></div>

  

  
  
</div>

  
    
<div class="member" id="height">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#height">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>height</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#191">
              image.js:191
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">height: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image height.</p></div>

  

  
  
</div>

  
    
<div class="member" id="id">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#id">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>id</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#177">
              image.js:177
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">id: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="isCover">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#isCover">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>isCover</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#243">
              image.js:243
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">isCover: boolean</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Whatever this image is book cover.</p></div>

  

  
  
</div>

  
    
<div class="member" id="type">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#type">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>type</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#198">
              image.js:198
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">type: <a href=%5Cnhentai-api%5Cnhentai-api%5CImageType.html>ImageType</a> = ImageTypes.JPEG</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image type.</p></div>

  

  
  
</div>

  
    
<div class="member" id="width">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#width">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>width</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#184">
              image.js:184
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">width: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image width.</p></div>

  

  
  
</div>

  
</div>

    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="parse">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Image.html#parse">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>parse</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#155">
              image.js:155
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static parse(image: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html">APIImage</a>, id: number) → {<a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Parse pure image object from API into class instance.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          image
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html">APIImage</a>
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Image object</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          id
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Image id (a.k.a. page number).</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        <td class="member-return__description"><p>Image instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#types>types</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#book>book</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#filename>filename</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#height>height</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#id>id</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#isCover>isCover</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#type>type</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#width>width</a></li>
    
  </ul>
</div>

  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html#parse>parse</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
