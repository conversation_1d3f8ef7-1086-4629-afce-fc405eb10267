#!/usr/bin/env node

/**
 * Test script to validate which URL patterns actually work
 * Testing with book ID 567015 to find the correct cover.jpg.webp pattern
 */

import { API, } from './dist/esm/bundle.mjs';
import https from 'https';


console.log('🧪 Testing URL validation for book ID 567015...\n');

const api = new API();

// Function to check if a URL returns a valid response
function checkURL(url) {
	return new Promise((resolve) => {
		const request = https.get(url, (response) => {
			resolve({
				url,
				status: response.statusCode,
				contentType: response.headers['content-type'],
				contentLength: response.headers['content-length'],
				valid: response.statusCode === 200,
			});
		});
		
		request.on('error', () => {
			resolve({
				url,
				status: 'ERROR',
				valid: false,
			});
		});
		
		request.setTimeout(5000, () => {
			request.destroy();
			resolve({
				url,
				status: 'TIMEOUT',
				valid: false,
			});
		});
	});
}

async function testBook567015() {
	try {
		console.log('📚 Fetching book 567015...');
		const book = await api.getBook(567015);
		
		console.log('✅ Book fetched successfully:');
		console.log(`   Title: ${book.title.pretty}`);
		console.log(`   Media ID: ${book.media}`);
		console.log(`   Cover type reported by API: ${book.cover.type.type}`);
		console.log(`   Cover extension reported by API: ${book.cover.type.extension}`);
		
		// Generate all possible URL patterns manually
		const mediaId = book.media;
		const baseURLs = [
			'https://t1.nhentai.net',
			'https://t2.nhentai.net',
			'https://t3.nhentai.net',
		];
		
		const extensionPatterns = [
			'cover.jpg',           // Simple JPG
			'cover.png',           // Simple PNG
			'cover.gif',           // Simple GIF
			'cover.webp',          // Simple WebP
			'cover.jpg.webp',      // JPG with WebP
			'cover.png.webp',      // PNG with WebP
			'cover.gif.webp',      // GIF with WebP
			'cover.webp.webp',     // WebP with WebP
		];
		
		console.log('\n🔍 Testing all possible URL patterns...');
		
		const testURLs = [];
		baseURLs.forEach(baseURL => {
			extensionPatterns.forEach(pattern => {
				testURLs.push(`${baseURL}/galleries/${mediaId}/${pattern}`);
			});
		});
		
		console.log(`   Testing ${testURLs.length} URL combinations...`);
		
		// Test all URLs
		const results = await Promise.all(testURLs.map(checkURL));
		
		// Filter valid results
		const validResults = results.filter(result => result.valid);
		const invalidResults = results.filter(result => !result.valid);
		
		console.log('\n✅ Valid URLs found:');
		if (validResults.length === 0) {
			console.log('   ❌ No valid URLs found!');
		} else {
			validResults.forEach((result, index) => {
				console.log(`   ${index + 1}. ${result.url}`);
				console.log(`      Status: ${result.status}, Type: ${result.contentType}, Size: ${result.contentLength}`);
			});
		}
		
		console.log('\n❌ Invalid URLs (first 5):');
		invalidResults.slice(0, 5).forEach((result, index) => {
			console.log(`   ${index + 1}. ${result.url} (${result.status})`);
		});
		
		// Compare with our API results
		const ourURL = api.getImageURL(book.cover);
		const ourOriginalURL = api.getImageURLOriginal(book.cover);
		
		console.log('\n🔧 Our API Results:');
		console.log(`   Generated URL: ${ourURL}`);
		console.log(`   Original URL: ${ourOriginalURL}`);
		
		const ourURLValid = validResults.some(result => result.url === ourURL);
		const ourOriginalURLValid = validResults.some(result => result.url === ourOriginalURL);
		
		console.log(`   Generated URL is valid: ${ourURLValid ? '✅' : '❌'}`);
		console.log(`   Original URL is valid: ${ourOriginalURLValid ? '✅' : '❌'}`);
		
		// Find the correct pattern
		if (validResults.length > 0) {
			const correctURL = validResults[0].url;
			const urlParts = correctURL.split('/');
			const filename = urlParts[urlParts.length - 1];
			
			console.log('\n🎯 Correct Pattern Found:');
			console.log(`   Filename: ${filename}`);
			console.log(`   Full URL: ${correctURL}`);
			
			// Analyze the pattern
			if (filename === 'cover.jpg.webp') {
				console.log('   📋 Pattern: cover.jpg.webp (JPG with WebP extension)');
				console.log('   💡 API reports "webp" but actual file is cover.jpg.webp');
			} else if (filename === 'cover.webp.webp') {
				console.log('   📋 Pattern: cover.webp.webp (WebP with WebP extension)');
			} else if (filename.includes('.webp')) {
				console.log(`   📋 Pattern: ${filename} (Double extension with WebP)`);
			} else {
				console.log(`   📋 Pattern: ${filename} (Simple extension)`);
			}
		}
		
		return {
			success: true,
			book,
			validResults,
			ourURL,
			ourURLValid,
		};
		
	} catch (error) {
		console.error('❌ Error fetching book:', error.message);
		return {
			success: false,
			error: error.message,
		};
	}
}

// Test the specific case
const result = await testBook567015();

if (result.success) {
	console.log('\n📊 Summary:');
	console.log(`   Valid URLs found: ${result.validResults.length}`);
	console.log(`   Our URL works: ${result.ourURLValid ? 'YES' : 'NO'}`);
	
	if (result.validResults.length > 0 && !result.ourURLValid) {
		console.log('\n🛠️  Enhancement Needed:');
		console.log('   Our current logic needs to be updated to handle this case');
		console.log('   The actual working URL pattern should be used in our detectCoverExtension()');
	}
} else {
	console.log('   ❌ Could not test due to error');
}

console.log('\n🏁 URL validation test completed!');
