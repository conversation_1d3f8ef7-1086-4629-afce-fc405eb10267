#!/usr/bin/env node

/**
 * Test script to check if our fix can handle cover.webp.webp case
 * Testing with book ID 577470 which has cover.webp.webp filename
 */

import { API, } from './dist/esm/bundle.mjs';


console.log('🧪 Testing double extension cases...\n');

const api = new API();

async function testCoverDoubleExtension(bookId, expectedPattern, description) {
	try {
		console.log('📚 Fetching book 577470...');
		const book = await api.getBook(567015);
		
		console.log('✅ Book fetched successfully:');
		console.log(`   Title: ${book.title.pretty}`);
		console.log(`   Media ID: ${book.media}`);
		console.log(`   Cover type reported by API: ${book.cover.type.type}`);
		console.log(`   Cover extension reported by API: ${book.cover.type.extension}`);
		
		// Test our current URL generation
		const generatedURL = api.getImageURL(book.cover);
		const originalURL = api.getImageURLOriginal(book.cover);
		const allVariants = api.getCoverURLVariants(book.cover);

		console.log('\n🔍 URL Generation Results:');
		console.log(`   Generated URL (our fix): ${generatedURL}`);
		console.log(`   Original URL (fallback): ${originalURL}`);
		console.log('\n🔗 All possible URL variants:');
		allVariants.forEach((url, index) => {
			console.log(`   ${index + 1}. ${url}`);
		});
		
		// The actual URL should be: https://t3.nhentai.net/galleries/3388169/cover.webp.webp
		const expectedPattern = /cover\.webp\.webp$/;
		const actualPattern = /cover\.webp$/;
		
		console.log('\n📊 Analysis:');
		console.log(`   Expected filename pattern: cover.webp.webp`);
		console.log(`   Generated URL matches expected: ${expectedPattern.test(generatedURL)}`);
		console.log(`   Generated URL matches simple webp: ${actualPattern.test(generatedURL)}`);
		
		if (expectedPattern.test(generatedURL)) {
			console.log('   ✅ SUCCESS: Our fix correctly handles cover.webp.webp!');
		} else if (actualPattern.test(generatedURL)) {
			console.log('   ❌ ISSUE: Our fix generates cover.webp instead of cover.webp.webp');
			console.log('   📝 This means our current logic needs enhancement for this edge case');
		} else {
			console.log('   ❓ UNEXPECTED: Generated URL has unexpected pattern');
		}
		
		// Test if we can detect this case
		console.log('\n🔧 Current Logic Analysis:');
		console.log(`   API reports type: ${book.cover.type.type}`);
		console.log(`   API reports extension: ${book.cover.type.extension}`);
		
		if (book.cover.type.extension === 'webp') {
			console.log('   📋 Current logic: Since API reports "webp", we use simple cover.webp');
			console.log('   💡 Issue: We need to detect when WebP files also have double extensions');
		}
		
		return {
			success: true,
			book,
			generatedURL,
			originalURL,
			needsEnhancement: !expectedPattern.test(generatedURL),
		};
		
	} catch (error) {
		console.error('❌ Error fetching book:', error.message);
		return {
			success: false,
			error: error.message,
		};
	}
}

// Test the specific case
const result = await testBook577470();

if (result.success && result.needsEnhancement) {
	console.log('\n🛠️  Enhancement Needed:');
	console.log('   Our current fix handles:');
	console.log('   ✅ cover.jpg.webp (API reports "j")');
	console.log('   ✅ cover.png.webp (API reports "p")');
	console.log('   ✅ cover.gif.webp (API reports "g")');
	console.log('   ✅ cover.webp (API reports "w" - simple case)');
	console.log('   ❌ cover.webp.webp (API reports "w" - double case)');
	
	console.log('\n💡 Solution Needed:');
	console.log('   We need to enhance detectCoverExtension() to also try');
	console.log('   cover.webp.webp when API reports "webp" but simple cover.webp fails');
	
	console.log('\n🔄 Suggested Enhancement:');
	console.log('   1. Try simple cover.webp first (current behavior)');
	console.log('   2. If that fails, try cover.webp.webp as fallback');
	console.log('   3. Or detect this pattern based on media ID ranges or other heuristics');
}

console.log('\n🎯 Test Summary:');
if (result.success) {
	console.log(`   Book 577470 cover URL: ${result.generatedURL}`);
	console.log(`   Handles cover.webp.webp: ${!result.needsEnhancement ? 'YES' : 'NO'}`);
	if (result.needsEnhancement) {
		console.log('   📝 Action needed: Enhance detectCoverExtension() for WebP double extensions');
	}
} else {
	console.log('   ❌ Could not test due to error');
}

console.log('\n🏁 Test completed!');
