
<!-- Generated by webdoc on 28/05/2025, 10.03.49 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: APIImage</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>APIImage</span>
    </div>
    <section class="document__title ">
      <h1>
        APIImage
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">typedef APIImage</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Image object from API.</p></div>

    

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from APIImage</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#h">static h</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#t">static t</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number | string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#w">static w</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="h">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIImage.html#h">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>h</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#7">
              image.js:7
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static h: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image height.</p></div>

  

  
  
</div>

  
    
<div class="member" id="t">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIImage.html#t">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>t</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#7">
              image.js:7
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static t: string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image type.</p></div>

  

  
  
</div>

  
    
<div class="member" id="w">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\APIImage.html#w">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>w</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\image.js.html#7">
              image.js:7
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static w: number | string</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Image width.</p></div>

  

  
  
</div>

  
</div>

    
    
    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#h>h</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#t>t</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CAPIImage.html#w>w</a></li>
    
  </ul>
</div>

  
  
  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
