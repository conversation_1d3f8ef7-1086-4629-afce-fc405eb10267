export type TagTypes = import("./tag").TagTypes;
/**
 * @typedef { import("./tag").TagTypes } TagTypes
 */
/**
 * @type {TagTypes}
 */
export const TagTypes: TagTypes;
import API from "./api";
import { Search } from "./search";
import { SearchSort } from "./search";
import Book from "./book";
import Image from "./image";
import { Tag } from "./tag";
export { API, Search, SearchSort, Book, Image, Tag };
