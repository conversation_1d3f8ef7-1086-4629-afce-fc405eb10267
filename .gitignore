# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# VS
.vs/
## User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates
## Build artifacts
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Git
.git/

# Node
node_modules/

# Code coverage
.nyc_output
coverage/

# Environment
.env

# Distribution
**/*.tgz

# Webdoc artifacts
/example.api.json
