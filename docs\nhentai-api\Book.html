
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: Book</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"reference"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs api-docs">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            
<main class="page-content">
  <div class="document">
    <div class="breadcrumb">
      
        nhentai-api
        &gt;
      
      
      <span>Book</span>
    </div>
    <section class="document__title ">
      <h1>
        Book
      </h1>
      
    </section>

    
<pre class="signature__container"><code class="signature">class Book</code></pre>


    <div class="document__brief"></div>
    <div class="document__description"><p>Class representing Book.</p></div>

    

    
      
<div class="members">
  <h2 class="members__category">Constructor</h2>
  <hr />

  
    
<div class="member" id="constructor">
  
  
<pre class="signature__container"><code class="signature">new Book(params: object) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Create book.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          params
        </td>
        <td class="member-parameter__type">
          object
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Book parameters.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.title
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CBookTitle.html">BookTitle</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Book title.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.id
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Book ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.media
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Book Media ID.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.favorites
        </td>
        <td class="member-parameter__type">
          number
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          0
        </td>
        <td class="member-parameter__description"><p>Book favours count.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.scanlator
        </td>
        <td class="member-parameter__type">
          string
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          ''
        </td>
        <td class="member-parameter__description"><p>Book scanlator.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.uploaded
        </td>
        <td class="member-parameter__type">
          Date
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Book upload date.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.tags
        </td>
        <td class="member-parameter__type">
          Array&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>&gt; | <a href="%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html">TagsArray</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          []
        </td>
        <td class="member-parameter__description"><p>Book tags.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.cover
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Book cover.</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          params.pages
        </td>
        <td class="member-parameter__type">
          Array&lt;<a href="%5Cnhentai-api%5Cnhentai-api%5CImage.html">Image</a>&gt;
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          []
        </td>
        <td class="member-parameter__description"><p>Book pages.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    

    

<div class="members">
  <h2 class="members__category">Summary</h2>
  <hr />
  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Properties from Book</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownBook.html>UnknownBook</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#Unknown">static Unknown</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownBook.html>UnknownBook</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#UnknownBook">static UnknownBook</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#artists">artists</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#categories">categories</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#characters">characters</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html>Image</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#cover">cover</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#favorites">favorites = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#groups">groups</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#id">id = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  boolean
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#isKnown">isKnown</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#languages">languages</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  number
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#media">media = 0</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html>Image</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#pages">pages = []</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#parodies">parodies</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>&gt;
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#pureTags">pureTags</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  string
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#scanlator">scanlator = ''</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html>TagsArray</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#tags">tags = []</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href=%5Cnhentai-api%5Cnhentai-api%5CBookTitle.html>BookTitle</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#title">title</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  Date
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#uploaded">uploaded = new Date(0)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  
  <div class="accordion accordion-active">
    <section class="accordion__toggle">
      <button>
        <img class="accordion__bg-inactive" src="\nhentai-api\icons\chevron-down.svg"/>
        <img class="accordion__bg-active" src="\nhentai-api\icons\chevron-up.svg"/>
      </button>
      <h3 class="members__subcategory">Methods from Book</h3>
    </section>
    <div class="accordion__content table-wrapper">
      <table class="summary">
        <tbody>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#parse">static parse(book: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html">APIBook</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html">TagsArray</a>
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#getTagsWith">getTagsWith(tag: object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#hasTag">hasTag(tag: <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, strict: boolean)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
            <tr>
              <td class="summary__signature">
                
              </td>
              <td>
                <section class="summary__signature">
                  <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html#hasTagWith">hasTagWith(tag: object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>)</a>
                </section>
                <section>
                  
                </section>
              </td>
            </tr>
          
        </tbody>
      </table>
    </div>
  </div>


  


  

  



  



  



</div>


    
<div class="members">
  <h2 class="members__category">Public Properties</h2>
  <hr />

  
    
<div class="member" id="Unknown">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#Unknown">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>Unknown</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#44">
              book.js:44
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static Unknown: <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownBook.html>UnknownBook</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Unknown book instance.</p></div>

  

  
  
</div>

  
    
<div class="member" id="UnknownBook">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#UnknownBook">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>UnknownBook</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#51">
              book.js:51
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static UnknownBook: <a href=%5Cnhentai-api%5Cnhentai-api%5CUnknownBook.html>UnknownBook</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>UnknownBook class.</p></div>

  

  
  
</div>

  
    
<div class="member" id="artists">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#artists">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>artists</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#291">
              book.js:291
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">artists: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Artist tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="categories">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#categories">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>categories</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#283">
              book.js:283
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">categories: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Category tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="characters">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#characters">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>characters</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#307">
              book.js:307
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">characters: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Character tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="cover">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#cover">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>cover</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#132">
              book.js:132
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">cover: <a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html>Image</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book cover.</p></div>

  

  
  
</div>

  
    
<div class="member" id="favorites">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#favorites">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>favorites</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#104">
              book.js:104
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">favorites: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book favours count.</p></div>

  

  
  
</div>

  
    
<div class="member" id="groups">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#groups">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>groups</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#315">
              book.js:315
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">groups: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Group tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="id">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#id">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>id</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#90">
              book.js:90
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">id: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="isKnown">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#isKnown">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>isKnown</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#191">
              book.js:191
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">isKnown: boolean</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Check whatever book is known.</p></div>

  

  
  
</div>

  
    
<div class="member" id="languages">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#languages">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>languages</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#323">
              book.js:323
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">languages: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Language tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="media">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#media">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>media</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#97">
              book.js:97
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">media: number = 0</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book Media ID.</p></div>

  

  
  
</div>

  
    
<div class="member" id="pages">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#pages">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>pages</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#138">
              book.js:138
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">pages: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CImage.html>Image</a>> = []</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book pages.</p></div>

  

  
  
</div>

  
    
<div class="member" id="parodies">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#parodies">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>parodies</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#299">
              book.js:299
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">parodies: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Parody tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="pureTags">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#pureTags">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>pureTags</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#275">
              book.js:275
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">pureTags: Array&lt;<a href=%5Cnhentai-api%5Cnhentai-api%5CTag.html>Tag</a>></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Pure tags (with type {TagType.Tag}).</p></div>

  

  
  
</div>

  
    
<div class="member" id="scanlator">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#scanlator">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>scanlator</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#111">
              book.js:111
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">scanlator: string = ''</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book scanlator.</p></div>

  

  
  
</div>

  
    
<div class="member" id="tags">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#tags">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>tags</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#125">
              book.js:125
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">tags: <a href=%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html>TagsArray</a> = []</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book tags.</p></div>

  

  
  
</div>

  
    
<div class="member" id="title">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#title">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>title</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#80">
              book.js:80
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">title: <a href=%5Cnhentai-api%5Cnhentai-api%5CBookTitle.html>BookTitle</a></code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book title.</p></div>

  

  
  
</div>

  
    
<div class="member" id="uploaded">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#uploaded">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>uploaded</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#118">
              book.js:118
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">uploaded: Date = new Date(0)</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Book upload date.</p></div>

  

  
  
</div>

  
</div>

    
    
<div class="members">
  <h2 class="members__category">Public Methods</h2>
  <hr />

  
    
<div class="member" id="parse">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#parse">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>parse</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#58">
              book.js:58
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">static parse(book: <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html">APIBook</a>) → {<a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Parse book object into class instance.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          book
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CAPIBook.html">APIBook</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Book.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CBook.html">Book</a>
        </td>
        <td class="member-return__description"><p>Book instance.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="getTagsWith">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#getTagsWith">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>getTagsWith</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#264">
              book.js:264
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">getTagsWith(tag: object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>) → {<a href="%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html">TagsArray</a>}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Get any tags with certain properties.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Tag.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
    
<div class="table-wrapper">
  <table class="member-returns">
    <caption class="member-returns__caption">Returns:</caption>
    <thead class="member-returns__head">
      <tr class="member-returns-header">
        <th class="member-returns-header__type">Type</th>
        <th class="member-returns-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-returns__body">
      <tr class="member-return">
        <td class="member-return__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CTagsArray.html">TagsArray</a>
        </td>
        <td class="member-return__description"></td>
      </tr>
    </tbody>
  </table>
</div>

  
  
</div>

  
    
<div class="member" id="hasTag">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#hasTag">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>hasTag</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#245">
              book.js:245
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">hasTag(tag: <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>, strict: boolean) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Check if book has certain tag.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        <th class="member-parameter-header__attributes">Attributes</th>
        <th class="member-parameter-header__default">Default</th>
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        <td class="member-parameter__attributes">
          
          
          
        </td>
        <td class="member-parameter__default">
          
        </td>
        <td class="member-parameter__description"><p>Tag</p></td>
      </tr><tr class="member-parameter">
        <td class="member-parameter__name">
          strict
        </td>
        <td class="member-parameter__type">
          boolean
        </td>
        <td class="member-parameter__attributes">
          <p>&lt;optional&gt;</p>
          
          
        </td>
        <td class="member-parameter__default">
          false
        </td>
        <td class="member-parameter__description"><p>Strict comparison.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
    
<div class="member" id="hasTagWith">
  
    <section class="member__title ">
      <a href="\nhentai-api\nhentai-api\Book.html#hasTagWith">
        <img
          src="\nhentai-api\icons\link.svg"
          width="16px"
          height="16px"
        />
      </a>
      <span class="member__title_content">
        <span>hasTagWith</span>
        
      </span>
      
        <span class="tag--source">
            <a href="\nhentai-api\nhentai-api\src\book.js.html#256">
              book.js:256
            </a>
        </span>
      
    </section>
  
  
<pre class="signature__container"><code class="signature">hasTagWith(tag: object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>) → {}</code></pre>

  <section class="member__tags">
    
  </section>

  <div class="member__brief"></div>
  <div class="member__description"><p>Check if book has any tags with certain properties.</p></div>

  
    
<div class="table-wrapper">
  <table class="member-parameters">
    <caption class="member-parameters__caption">Parameters:</caption>
    <thead class="member-parameters__head">
      <tr class="member-parameter-header">
        <th class="member-parameter-header__name">Name</th>
        <th class="member-parameter-header__type">Type</th>
        
        
        <th class="member-parameter-header__description">Description</th>
      </tr>
    </thead>
    <tbody class="member-parameters__body">
      <tr class="member-parameter">
        <td class="member-parameter__name">
          tag
        </td>
        <td class="member-parameter__type">
          object | <a href="%5Cnhentai-api%5Cnhentai-api%5CTag.html">Tag</a>
        </td>
        
        
        <td class="member-parameter__description"><p>Tag.</p></td>
      </tr>
    </tbody>
  </table>
</div>

  

  
  
</div>

  
</div>

    
    
  </div>
  <div class="footer-gap" style="min-height: 48px;"></div>
  


  <footer class="footer" id="footer-mount-point"></footer>

</main>

<hr class="page-r-divider" />
<nav class="page-members-explorer">
  <div class="page-members-explorer-category">
    <section class="page-members-explorer-category__title">
      Summary
    </section>
  </div>
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Properties</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#Unknown>Unknown</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#UnknownBook>UnknownBook</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#artists>artists</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#categories>categories</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#characters>characters</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#cover>cover</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#favorites>favorites</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#groups>groups</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#id>id</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#isKnown>isKnown</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#languages>languages</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#media>media</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#pages>pages</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#parodies>parodies</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#pureTags>pureTags</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#scanlator>scanlator</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#tags>tags</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#title>title</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#uploaded>uploaded</a></li>
    
  </ul>
</div>

  
  

<div class="page-members-explorer-category">
  <section class="page-members-explorer-category__title">Public Methods</section>
  <ul class="page-members-explorer-category__items">
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#parse>parse</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#getTagsWith>getTagsWith</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#hasTag>hasTag</a></li>
    
      <li class="page-members-explorer__item"><a href=%5Cnhentai-api%5Cnhentai-api%5CBook.html#hasTagWith>hasTagWith</a></li>
    
  </ul>
</div>

  
  
</nav>

            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
