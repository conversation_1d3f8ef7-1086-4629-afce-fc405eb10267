
<!-- Generated by webdoc on 07/06/2025, 14.10.57 Asia/Makassar -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>{ <i>webdoc</i> }: error.js</title>

    

    
        <link type="text/css" rel="stylesheet" href="/nhentai-api/styles/index.css" />
    

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&family=Roboto+Mono&family=Source+Code+Pro&display=swap" />
    <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/10.0.3/styles/googlecode.min.css">
    

    

    
      <script>
        window.appData = {
          applicationName: `{ <i>webdoc</i> }`,
          appBar: {"items":{"reference":{"name":"API Reference","uri":"\\nhentai-api\\index.html"}},"current":"sources"},
          explorerTree: "/nhentai-api/explorer/reference.json",
          siteRoot: "nhentai-api",
          integrations: JSON.parse(`{}`),
        };
      </script>
      <script crossorigin src="https://unpkg.com/react@16/umd/react.development.js"></script>
      <script crossorigin src="https://unpkg.com/react-dom@16/umd/react-dom.development.js"></script>
      
      <script src="/nhentai-api/scripts/default-template.js"></script>
    
  </head>
  <body class="root">
    <div class="docs sources">
      <article class="page">
        <div class="header-content-container">
          <section class="header" id="header-mount-point">
            
          </section>
          <div class="header-content-container__content">
            <div class="page-explorer" id="explorer-mount-point">
              
            </div>
            
            

<main class="page-content">
  <div class="source">
    <div class="source__split">
      <ul>
        
          <li id="1">
            <a href="#1">1</a>
          </li>
        
          <li id="2">
            <a href="#2">2</a>
          </li>
        
          <li id="3">
            <a href="#3">3</a>
          </li>
        
          <li id="4">
            <a href="#4">4</a>
          </li>
        
          <li id="5">
            <a href="#5">5</a>
          </li>
        
          <li id="6">
            <a href="#6">6</a>
          </li>
        
          <li id="7">
            <a href="#7">7</a>
          </li>
        
          <li id="8">
            <a href="#8">8</a>
          </li>
        
          <li id="9">
            <a href="#9">9</a>
          </li>
        
          <li id="10">
            <a href="#10">10</a>
          </li>
        
          <li id="11">
            <a href="#11">11</a>
          </li>
        
          <li id="12">
            <a href="#12">12</a>
          </li>
        
          <li id="13">
            <a href="#13">13</a>
          </li>
        
          <li id="14">
            <a href="#14">14</a>
          </li>
        
          <li id="15">
            <a href="#15">15</a>
          </li>
        
          <li id="16">
            <a href="#16">16</a>
          </li>
        
          <li id="17">
            <a href="#17">17</a>
          </li>
        
          <li id="18">
            <a href="#18">18</a>
          </li>
        
          <li id="19">
            <a href="#19">19</a>
          </li>
        
          <li id="20">
            <a href="#20">20</a>
          </li>
        
          <li id="21">
            <a href="#21">21</a>
          </li>
        
          <li id="22">
            <a href="#22">22</a>
          </li>
        
          <li id="23">
            <a href="#23">23</a>
          </li>
        
          <li id="24">
            <a href="#24">24</a>
          </li>
        
          <li id="25">
            <a href="#25">25</a>
          </li>
        
          <li id="26">
            <a href="#26">26</a>
          </li>
        
          <li id="27">
            <a href="#27">27</a>
          </li>
        
          <li id="28">
            <a href="#28">28</a>
          </li>
        
          <li id="29">
            <a href="#29">29</a>
          </li>
        
          <li id="30">
            <a href="#30">30</a>
          </li>
        
          <li id="31">
            <a href="#31">31</a>
          </li>
        
          <li id="32">
            <a href="#32">32</a>
          </li>
        
          <li id="33">
            <a href="#33">33</a>
          </li>
        
          <li id="34">
            <a href="#34">34</a>
          </li>
        
          <li id="35">
            <a href="#35">35</a>
          </li>
        
          <li id="36">
            <a href="#36">36</a>
          </li>
        
          <li id="37">
            <a href="#37">37</a>
          </li>
        
          <li id="38">
            <a href="#38">38</a>
          </li>
        
          <li id="39">
            <a href="#39">39</a>
          </li>
        
          <li id="40">
            <a href="#40">40</a>
          </li>
        
          <li id="41">
            <a href="#41">41</a>
          </li>
        
          <li id="42">
            <a href="#42">42</a>
          </li>
        
          <li id="43">
            <a href="#43">43</a>
          </li>
        
      </ul>
      <pre><code><span class="hljs-comment">// eslint-disable-next-line no-unused-vars</span>
<span class="hljs-keyword">import</span> { IncomingMessage, } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;http&#x27;</span>;


<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">APIError</span> <span class="hljs-keyword">extends</span> <span class="hljs-title">Error</span> </span>{
	<span class="hljs-comment">/**
	 * Original error.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{?Error}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	originalError = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * HTTP response.
	 * <span class="hljs-doctag">@type <span class="hljs-type">{IncomingMessage}</span>
</span>
	 * <span class="hljs-doctag">@default <span class="hljs-variable">null</span></span>
	 */</span>
	httpResponse = <span class="hljs-literal">null</span>;

	<span class="hljs-comment">/**
	 * Error message.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{string}</span> </span>message Message.
	 */</span>
	<span class="hljs-function"><span class="hljs-title">constructor</span>(<span class="hljs-params">message = <span class="hljs-string">&#x27;Unknown error&#x27;</span></span>)</span> {
		<span class="hljs-built_in">super</span>(message);
	}

	<span class="hljs-comment">/**
	 * Absorb error.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{Error}</span> </span>[error=null] Original error.
	 * <span class="hljs-doctag">@param <span class="hljs-type">{?IncomingMessage}</span> </span>[httpResponse=null] HTTP response.
	 * <span class="hljs-doctag">@returns <span class="hljs-type">{APIError}</span>
</span>
	 */</span>
	<span class="hljs-keyword">static</span> <span class="hljs-function"><span class="hljs-title">absorb</span>(<span class="hljs-params">error, httpResponse = <span class="hljs-literal">null</span></span>)</span> {
		<span class="hljs-keyword">return</span> <span class="hljs-built_in">Object</span>.assign(<span class="hljs-keyword">new</span> APIError(error.message), {
			<span class="hljs-attr">originalError</span>: error,
			httpResponse,
		});
	}
}

<span class="hljs-keyword">export</span> <span class="hljs-keyword">default</span> APIError;
</code></pre>
    </div>
  </div>
</main>
            <!-- footer in content -->
          </div>
          <div class="bottom-banner">
              
          </div>
        </div>
      </article>
    </body>
  </div>
</html>
